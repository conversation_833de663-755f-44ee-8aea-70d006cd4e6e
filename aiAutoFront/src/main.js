import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
import '@/styles/theme/element-dark.scss';
import '@/styles/index.scss';
import '@/styles/tailwind.css';
import 'virtual:svg-icons-register'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'



// const app=createApp(App)
// app.use(router)

// app.mount('#app')
// app.use(ElementPlus)
//app.use(VueTreeList);
import dayjs from 'dayjs';



import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// 引入公共组件
import CommonComponents from '@/components/common'

const app = createApp(App);
const pinia = createPinia()
app.config.globalProperties.$dayjs = dayjs; // 全局引入，原型挂载

// 使用icon
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册公共组件
app.use(CommonComponents)

router.beforeEach((to, from, next) => {
    let token = localStorage.getItem('token');

    if (!token && to.path !== '/ssoLogin') {
        console.log(to.path);
        next({ path: '/ssoLogin'})
    }else{
        next()
    }



})
// 创建实例
const setupAll = async () => {
    app.use(pinia).use(router).use(ElementPlus, {
  locale: zhCn
})
    // setupElementPlus(app)
    app.mount('#app');
};
setupAll();

