import { ref} from "vue";
import dayjs from 'dayjs';
import { defineStore } from 'pinia'
import { materialList,addMateria,updateMateria,delMateria ,materialtype , materialsource,copyMateria} from '@/api/materialApi';
import{serviceList} from '@/api/projectApi'
import { ElMessage, ElMessageBox } from 'element-plus'


export const materialStore = defineStore('materialstore', {
    state: () => ({
      defaultProjectId:ref(null),
      dataListPage:1,
        dataTotal:0,
        pageSize:20,
        updateVisible:false,
         updateForm :{
            id: null,
            content_question: "",
          content_answer: "",
          content: "",
          source: 0,
          type: 0,
          creator: 1,
          project: 0,
          share: null
        },
        materialtypeValue:null,
        materialsourceValue:null,
        materialtypeData:[],
        materialsourceData:[],
        projectvalue:'',
        projectData:[],
        materialTableData:[],
        dialogMaterialVisible:false,
        addRuleForm:{
          content_question: "",
          content_answer: "",
          content: "",
        source: null,
       type: null,
      creator: JSON.parse(localStorage.getItem('loginvalue')) ,
     project: null,
      share: null
        },
        formLabelAlign : {
          id:'',
          name:'',
          description:'',
          create_time:'',
          update_time:'',
        },
        formLabelAlign2 : {
          id:'',
          name:'',
          description:'',
          create_time:'',
          update_time:'',
        },
        rules: {
          name: [
            { required: true, message: '请输入名称', trigger: 'blur' },
            { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
          ],
          description: [
            { required: true, message: '请输入描述', trigger: 'blur' },
            { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
          ]
        }
    }),
    actions: {
        async funDelMaterial(val){
          try {
            await ElMessageBox.confirm(
              `确定要删除物料 "${val.content_question || val.content || '此项'}" 吗？此操作不可撤销。`,
              '删除确认',
              {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                confirmButtonClass: 'el-button--danger',
              }
            );

            // 执行删除
            const res = await delMateria({id: val.id});
            if (res.success) {
              ElMessage.success('删除成功');
              this.getserviceList()
              // this.getMaterialList(val.id);
            } else {
              ElMessage.error(res.msg || '删除失败');
            }
          } catch (error) {
            if (error !== 'cancel') {
              ElMessage.error('删除操作失败');
            }
          }
        },
         updateClose(){
           this. updateVisible=false;
          },

         handlerun(val){

            this.updateVisible=true;
            this.updateForm = Object.assign({}, val);
             },

        putUpdateMateria(){

            let params = {

                    content_question: this.updateForm.content_question,
                    content_answer: this.updateForm.content_answer,
                    content: this.updateForm.content,
                    source: this.updateForm.source_id,
                    type: this.updateForm.type_id,
                    creator: this.updateForm.creator_id,
                    project: this.updateForm.project_id,
                    share: this.updateForm.share,
                    id: this.updateForm.id

            };

          updateMateria(params).then((res) => {
            if(res.success){
              this.getMaterialList(this.updateForm.project_id)
            }else{
              ElMessage.error(res.msg)
            }


            this.updateVisible=false
          })
        },
        getserviceList() {
            let params = {
                page:1,
                page_size:100,
              };
              //请求服务接口
              serviceList({...params}).then((res) => {
                this.projectData=res.resp.map(item => {
                  return {
                  label:item.name,
                  value:item.id
                  }
                })
                this.defaultProjectId=res.resp[0].id
                this.getMaterialList(this.defaultProjectId)


              })
        },


       handleCurrentChange(val){
          this.dataListPage=val;
          this.getMaterialList(this.projectvalue);
      },

      handleSizeChange(val){
          this.pageSize = val;
          this.dataListPage = 1; // 重置到第一页
          this.getMaterialList(this.projectvalue);
      },
       async getMaterialList(val) {
        console.log('val======',val);
        if(val===''){
          ElMessage.error('请选择项目');
        }else{
             let params = {
            project_id: val,
            page: this.dataListPage,
            page_size: this.pageSize
        };

        try {
          const res = await materialList({...params});
          this.dataTotal = res.total;
          this.materialTableData = res.resp.map(item => {
            return {
              ...item,
              create_time: dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss'),
              update_time: dayjs(item.update_time).format('YYYY-MM-DD HH:mm:ss')
            }
          });
        } catch (error) {
          console.error('获取物料列表失败:', error);
          ElMessage.error('获取物料列表失败');
        }
        }



      },
      handleProject(){
        this.getMaterialList(this.projectvalue)
      },
      resetButton(){
        this.projectvalue=''
         this.getserviceList();
      },
      getmaterialtype(){
        materialtype().then((res) => {
          this.materialtypeData=res.resp.map(item => {
            return {
            label:item.name,
            value:item.id
            }
          })

        })
      },
      getmaterialsource(){
        materialsource().then((res) => {
          this.materialsourceData=res.resp.map(item => {
            return {
            label:item.name,
            value:item.id
            }
          })
        })
      },
      addMaterialForm(){
        let params={

          content_question: this.addRuleForm.content_question,
          content_answer:this.addRuleForm.content_answer,
          content: this.addRuleForm.content ,
        source: this.addRuleForm.source,
       type: this.addRuleForm.type,
      creator: JSON.parse(localStorage.getItem('loginvalue')) ,
     project: this.addRuleForm.project,
      share: this.addRuleForm.share,


        }
        addMateria(params).then((res) => {
          this.getMaterialList(this.addRuleForm.project)
          this.dialogMaterialVisible=false
        })

      },
      copyMateriaData(val){
        copyMateria(val.id).then((res) => {
         if(res.success){
          this.getMaterialList(val.project_id)
         }else{
          ElMessage.error(res.msg)
         }

        })
      },


    },
  getters: {

  }

})

