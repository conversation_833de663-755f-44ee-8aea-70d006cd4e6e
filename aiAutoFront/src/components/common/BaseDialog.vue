<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    :width="width"
    :before-close="handleClose"
    :class="dialogClass"
    :destroy-on-close="destroyOnClose"
  >
    <slot name="content">
      <el-form
        v-if="showForm"
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-width="labelWidth"
        :label-position="labelPosition"
        :class="formClass"
      >
        <slot name="form-items" :form-data="formData" :form-ref="formRef" />
      </el-form>
      <slot v-else />
    </slot>

    <template #footer>
      <slot name="footer">
        <div class="dialog-footer">
          <el-button @click="handleCancel" :disabled="loading">
            {{ cancelText }}
          </el-button>
          <el-button
            type="primary"
            @click="handleConfirm"
            :loading="loading"
            :disabled="confirmDisabled"
          >
            {{ confirmText }}
          </el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '对话框'
  },
  width: {
    type: String,
    default: '600px'
  },
  dialogClass: {
    type: String,
    default: ''
  },
  destroyOnClose: {
    type: Boolean,
    default: true
  },
  showForm: {
    type: Boolean,
    default: true
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
  labelWidth: {
    type: String,
    default: '100px'
  },
  labelPosition: {
    type: String,
    default: 'right'
  },
  formClass: {
    type: String,
    default: ''
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  confirmDisabled: {
    type: Boolean,
    default: false
  },
  validateOnConfirm: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'close'])

const formRef = ref(null)
const loading = ref(false)

const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}

const handleCancel = () => {
  handleClose()
  emit('cancel')
}

const handleConfirm = async () => {
  if (props.validateOnConfirm && props.showForm && formRef.value) {
    try {
      await formRef.value.validate()
    } catch (error) {
      ElMessage.warning('请检查表单填写是否正确')
      return
    }
  }

  loading.value = true
  try {
    await emit('confirm', props.formData)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  formRef,
  loading,
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button:first-child {
  margin-right: 10px;
}
</style>
