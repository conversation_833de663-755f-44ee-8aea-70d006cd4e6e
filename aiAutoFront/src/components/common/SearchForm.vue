<template>
  <div class="search-form">
    <el-form
      ref="formRef"
      :model="searchData"
      :inline="true"
      :size="size"
      :class="formClass"
    >
      <slot :search-data="searchData" :form-ref="formRef" />
      
      <el-form-item>
        <el-button
          type="primary"
          :icon="SearchIcon"
          @click="handleSearch"
          :loading="loading"
        >
          {{ searchText }}
        </el-button>
        <el-button
          :icon="RefreshIcon"
          @click="handleReset"
          :disabled="loading"
        >
          {{ resetText }}
        </el-button>
        <slot name="extra-buttons" :search-data="searchData" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Search as SearchIcon, Refresh as RefreshIcon } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  size: {
    type: String,
    default: 'default'
  },
  formClass: {
    type: String,
    default: ''
  },
  searchText: {
    type: String,
    default: '查询'
  },
  resetText: {
    type: String,
    default: '重置'
  },
  loading: {
    type: Boolean,
    default: false
  },
  resetToDefault: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

const formRef = ref(null)
const searchData = reactive({ ...props.modelValue })

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  Object.assign(searchData, newVal)
}, { deep: true })

// 监听内部数据变化
watch(searchData, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 搜索处理
const handleSearch = () => {
  emit('search', { ...searchData })
}

// 重置处理
const handleReset = () => {
  // 重置到默认值
  const defaultData = { ...props.resetToDefault }
  Object.keys(searchData).forEach(key => {
    if (defaultData.hasOwnProperty(key)) {
      searchData[key] = defaultData[key]
    } else {
      // 根据类型设置默认值
      if (Array.isArray(searchData[key])) {
        searchData[key] = []
      } else if (typeof searchData[key] === 'string') {
        searchData[key] = ''
      } else if (typeof searchData[key] === 'number') {
        searchData[key] = 0
      } else {
        searchData[key] = null
      }
    }
  })
  
  // 清除表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  
  emit('reset', { ...searchData })
}

// 暴露方法给父组件
defineExpose({
  formRef,
  searchData,
  handleSearch,
  handleReset
})
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.search-form .el-form-item:last-child {
  margin-left: 20px;
}
</style>
