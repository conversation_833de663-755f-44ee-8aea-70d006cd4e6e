<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    :label-width="labelWidth"
    :label-position="labelPosition"
    :inline="inline"
    :size="size"
    :disabled="disabled"
    :class="formClass"
  >
    <slot :form-data="formData" :form-ref="formRef" />
  </el-form>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  rules: {
    type: Object,
    default: () => ({})
  },
  labelWidth: {
    type: String,
    default: '100px'
  },
  labelPosition: {
    type: String,
    default: 'right'
  },
  inline: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'default'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  formClass: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'validate', 'submit'])

const formRef = ref(null)
const formData = ref({ ...props.modelValue })
const formRules = ref({ ...props.rules })

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  formData.value = { ...newVal }
}, { deep: true })

// 监听内部数据变化
watch(formData, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 监听规则变化
watch(() => props.rules, (newVal) => {
  formRules.value = { ...newVal }
}, { deep: true })

// 表单验证
const validate = async () => {
  if (!formRef.value) {
    throw new Error('表单引用不存在')
  }
  return await formRef.value.validate()
}

// 验证指定字段
const validateField = async (prop) => {
  if (!formRef.value) {
    throw new Error('表单引用不存在')
  }
  return await formRef.value.validateField(prop)
}

// 重置表单
const resetFields = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 清除验证
const clearValidate = (props) => {
  if (formRef.value) {
    formRef.value.clearValidate(props)
  }
}

// 提交表单
const submit = async () => {
  try {
    await validate()
    emit('submit', formData.value)
    return formData.value
  } catch (error) {
    if (error !== false) {
      ElMessage.warning('请检查表单填写是否正确')
    }
    throw error
  }
}

// 暴露方法给父组件
defineExpose({
  formRef,
  formData,
  validate,
  validateField,
  resetFields,
  clearValidate,
  submit
})
</script>

<style scoped>
/* 表单样式可以在这里自定义 */
</style>
