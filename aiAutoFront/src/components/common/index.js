/**
 * 公共组件入口文件
 */

// 基础组件
import BaseDialog from './BaseDialog.vue'
import BaseTable from './BaseTable.vue'
import BasePagination from './BasePagination.vue'
import BaseForm from './BaseForm.vue'
import SearchForm from './SearchForm.vue'

// 组件列表
const components = [
  BaseDialog,
  BaseTable,
  BasePagination,
  BaseForm,
  SearchForm
]

// 安装函数
const install = (app) => {
  components.forEach(component => {
    app.component(component.name || component.__name, component)
  })
}

// 导出组件
export {
  BaseDialog,
  BaseTable,
  BasePagination,
  BaseForm,
  SearchForm
}

// 默认导出安装函数
export default {
  install
}
