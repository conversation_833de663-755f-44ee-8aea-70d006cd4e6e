<template>
  <div class="base-table">
    <el-table
      :data="data"
      :loading="loading"
      :height="height"
      :max-height="maxHeight"
      :size="size"
      :empty-text="emptyText"
      :class="tableClass"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
    >


      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        label="序号"
        width="50"
        align="center"
        :index="getIndex"
      />

      <!-- 动态列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :sortable="column.sortable"
        :align="column.align || 'left'"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="scope">
          <slot
            :name="column.prop"
            :row="scope.row"
            :column="column"
            :$index="scope.$index"
          >
            <!-- 默认显示 -->
            <span v-if="!column.formatter">
              {{ scope.row[column.prop] }}
            </span>
            <!-- 自定义格式化 -->
            <span v-else>
              {{ column.formatter(scope.row, column, scope.row[column.prop], scope.$index) }}
            </span>
          </slot>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        v-if="showActions"
        label="操作"
        :width="actionWidth"
        :min-width="actionMinWidth"
        :fixed="actionFixed"
        align="center"
      >
        <template #default="scope">
          <slot name="actions" :row="scope.row" :$index="scope.$index">
            <div class="action-buttons">
              <el-button
                v-for="action in actions"
                :key="action.key"
                :type="action.type || 'primary'"
                :size="action.size || 'small'"
                :icon="action.icon"
                :disabled="action.disabled && action.disabled(scope.row)"
                :loading="action.loading && action.loading(scope.row)"
                link
                @click="handleAction(action.key, scope.row, scope.$index)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div v-if="showPagination" class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes"
        :total="pagination.total"
        :layout="pagination.layout"
        :pager-count="pagination.pagerCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />


         <!-- <el-pagination
    :page-size=20
    :pager-count="11"
    layout="prev, pager, next"
    :total=serviceListTotal
    @current-change="handleCurrentChange"
   style="float: right;margin-top: 0.8%;"
  /> -->
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  height: {
    type: [String, Number],
    default: undefined
  },
  maxHeight: {
    type: [String, Number],
    default: undefined
  },
  stripe: {
    type: Boolean,
    default: true
  },
  border: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default'
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  tableClass: {
    type: String,
    default: ''
  },
  showSelection: {
    type: Boolean,
    default: false
  },
  showIndex: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: true
  },
  actions: {
    type: Array,
    default: () => []
  },
  actionWidth: {
    type: [String, Number],
    default: undefined
  },
  actionMinWidth: {
    type: [String, Number],
    default: '120'
  },
  actionFixed: {
    type: [String, Boolean],
    default: false
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  pagination: {
    type: Object,
    default: () => ({
      currentPage: 1,
      pageSize: 20,
      total: 0,
      pageSizes: [10, 20, 50, 100],
      layout: 'prev, pager, next',
      pagerCount:11
    })
  }
})

const emit = defineEmits([
  'selection-change',
  'sort-change',
  'row-click',
  'action',
  'size-change',
  'current-change'
])

const getIndex = (index) => {
  return (props.pagination.currentPage - 1) * props.pagination.pageSize + index + 1
}

const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleSortChange = (sortInfo) => {
  emit('sort-change', sortInfo)
}

const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event)
}

const handleAction = (actionKey, row, index) => {
  emit('action', actionKey, row, index)
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  emit('current-change', page)
}
</script>

<style scoped>
.base-table {
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.pagination-container .el-pagination {
  justify-content: flex-end;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  min-width: auto;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}
</style>
