<template>
  <el-tabs
    v-model="frameName"
    type="border-card"
    style="width: 100%; margin-top: 1%"
    @tab-click="handleClick"
  >
    <el-tab-pane label="框架自动评估" name="frameEvaluate">
      <el-tabs
        v-model="activeName01"
        class="demo-tabs"
        @tab-click="handleClick01"
      >
        <el-tab-pane label="RAG" name="first">
          <el-row>
            <el-col :span="24">
              <span>项目信息</span>
              <el-cascader
                v-model="structureValue"
                :options="structureData"
                :props="props1"
                clearable
              />

               <span>物料信息</span><el-select
                @focus="requestcaseList(structureValue[2])"
                v-model="ragListParam.caseValue"
                filterable
                placeholder="Select"
                style="width: 200px"
              >
                <el-option
                  v-for="item in ragListParam.caseoptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
             <span>关键字</span>
              <el-input
                v-model="ragListParam.keyword"
                class="keyword"
                placeholder="请输入内容"
                style="width: 200px"
              ></el-input>
<span>版本</span>
              <el-input
                v-model="ragListParam.version"
                class="keyword"
                placeholder="请输入内容"
                style="width: 200px"
              ></el-input>
<span>指标</span>              <el-select
                v-model="ragListParam.metricValue"
                placeholder="Select"
                style="width: 200px"
              >
                <el-option
                  v-for="item in ragListParam.metric"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
<span>分数</span>              <el-select
                v-model="ragListParam.scoreValue"
                placeholder="Select"
                style="width: 200px"
              >
                <el-option
                  v-for="item in ragListParam.score_range"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button
                @click="testarglist()"
                style="color: #615ced; width: 3%; margin-left: 1%"
                plain
                >查询
              </el-button>
              <el-button
                @click="resetRagForm()"
                style="color: #615ced; width: 3%; margin-left: 1%"
                plain
                >重置
              </el-button>
            </el-col>
          </el-row>

          <el-table
            :data="autoEvaluationData"
            style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 90%"
          >
            <el-table-column
              v-for="(column, index) in autoEvaluationColumns"
              :key="index"
              :prop="column.prop"
              :label="column.label"
              min-width="10%"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column label="分数" min-width="8%">
              <template #default="{ row }">
                <el-input
                  v-model="row.score"
                  class="w-50 m-2"
                  size="small"
                  type="number"
                  :min="0"
                  :max="1"
                  step="0.01"
                  placeholder="0-1"
                  @input="validateScore(row)"
                  @blur="changefs(row)"
                />
              </template>
            </el-table-column>

            <el-table-column label="操作" min-width="20%">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleDeatil(scope.row)"
                  style="margin-right: 8px;"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- RAG分页组件 -->
          <el-footer style="float: right; background: #fff; width: 100%">
            <el-pagination
              :page-size="ragListParam.size"
              :pager-count="11"
              layout="prev, pager, next"
              :total="ragListTotal"
              :current-page="ragPage"
              @current-change="handleRagPageChange"
              style="float: right; margin-top: 0.8%"
            />
          </el-footer>
        </el-tab-pane>
        <el-tab-pane label="LLM" name="second">
          <el-col :span="24">
<span>项目信息</span>              <el-cascader
                v-model="llmstructureValue"
                :options="structureData"
                :props="props1"
                clearable
              />

            <span>关键字</span>
              <el-input
                v-model="llmListParam.keyword"
                class="keyword"
                placeholder="请输入内容"
                style="width: 200px"
              ></el-input>
<span>版本</span>
              <el-input
                v-model="llmListParam.version"
                class="keyword"
                placeholder="请输入内容"
                style="width: 200px"
              ></el-input>
<span>指标</span>              <el-select
                v-model="llmListParam.metricValue"
                placeholder="Select"
                style="width: 200px"
              >
                <el-option
                  v-for="item in llmListParam.metric"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
<span>分数</span>              <el-select
                v-model="llmListParam.scoreValue"
                placeholder="Select"
                style="width: 200px"
              >
                <el-option
                  v-for="item in llmListParam.score_range"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button
                style="color: #615ced; width: 3%; margin-left: 1%"
                @click="llmquery()"
                plain
                >查询
              </el-button>
              <el-button
                @click="resetLlmForm()"
                style="color: #615ced; width: 3%; margin-left: 1%"
                plain
                >重置
              </el-button>
          </el-col>

          <el-table
            :data="autoEvaluationData2"
            style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 90%"
          >
            <el-table-column
              v-for="(column, index) in autoEvaluationColumns2"
              :key="index"
              :prop="column.prop"
              :label="column.label"
              min-width="10%"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column label="操作" min-width="20%">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="llmHandleDeatil(scope.row)"
                  style="margin-right: 8px;"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- LLM分页组件 -->
          <el-footer style="float: right; background: #fff; width: 100%">
            <el-pagination
              :page-size="llmListParam.size"
              :pager-count="11"
              layout="prev, pager, next"
              :total="llmListTotal"
              :current-page="llmPage"
              @current-change="handleLlmPageChange"
              style="float: right; margin-top: 0.8%"
            />
          </el-footer>
        </el-tab-pane>
        <!-- <el-footer style="float: right; background: #fff; width: 100%">
          <el-pagination
            :page-size=10
            :pager-count="11"
            layout="prev, pager, next"
            :total="llmTotal"
            @current-change="handleCurrentChange"
            style="float: right; margin-top: 0.8%"
          />
        </el-footer> -->
      </el-tabs>
    </el-tab-pane>

    <el-tab-pane label="人工评估" name="humaneEvaluate">
      <el-row>
        <el-col :span="24">
        <span>项目信息</span>
          <el-tree-select
            v-model="suiteValue"
            :props="treeProps"
            :load="loadNode"
            lazy
            style="width: 200px"
            placeholder="请选择层级数据"
          />
          <span>版本</span>
          <el-input
            v-model="manualVersion"

            placeholder="请输入内容"
            style="width: 15%"
            @blur="changeProjectId"
          ></el-input>
<span>关键字</span>
          <el-input
            v-model="manualKey"

            placeholder="请输入内容"
            style="width: 15%"
          ></el-input>
          <el-button
            @click="humanList()"
            style="color: #615ced; margin-left: 1%"
            plain
            >查询
          </el-button>

          <el-button
            @click="reset()"
            style="color: #615ced; margin-left: 1%"
            plain
            >重置
          </el-button>
        </el-col>
      </el-row>
      <el-table
        :data="Case"
        style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 90%"
      >
        <el-table-column
          v-for="(column, index) in autoEvaluationColumns3"
          :key="index"
          :prop="column.prop"
          :label="column.label"
          min-width="30%"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column label="操作" min-width="20%">
          <template #default="scope">
            <el-button
              type="warning"
              size="small"
              @click="handlerun(scope.row)"
              style="margin-right: 8px;"
              >编辑</el-button
            >
            <el-button
              type="primary"
              size="small"
              @click="detail(scope.row)"
              style="margin-right: 8px;"
              >详情</el-button
            >
            <!-- <el-button type="info" size="small">报告</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <el-footer style="float: right; background: #fff; width: 100%">
        <el-pagination
          :page-size="10"
          :pager-count="11"
          layout="prev, pager, next"
          :total="serviceListTotal"
          @current-change="handleCurrentChange01"
          style="float: right; margin-top: 0.8%"
        />
      </el-footer>
    </el-tab-pane>
  </el-tabs>

  <el-dialog
    title="数据评估"
    :model-value="dialogVisible"
    :before-close="handleClose"
    width="50%"
    height="90%"
  >
    <el-row>
      <el-col :span="12">
        <div class="dialog-label framework-type-label"></div>
        <el-input
          placeholder="请输入内容"
          style="width: 50%; margin-left: 2%"
        ></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 2%">
      <el-col :span="12">
        <div class="dialog-label test-data-label"></div>
        <el-input
          placeholder="请输入内容"
          style="width: 50%; margin-left: 2%"
        ></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 2%">
      <el-col :span="12">
        <div class="dialog-label question-label"></div>
        <el-input
          placeholder="请输入内容"
          style="width: 50%; margin-left: 13%"
        ></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 2%">
      <el-col :span="12">
        <div class="dialog-label context-label"></div>
        <el-input
          placeholder="请输入内容"
          style="width: 50%; margin-left: 4%"
        ></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 2%">
      <el-col :span="12">
        <div class="dialog-label generated-answer-label"></div>
        <el-input
          placeholder="请输入内容"
          style="width: 50%; margin-left: 7%"
        ></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 2%">
      <el-col :span="12">
        <div class="dialog-label standard-answer-label"></div>
        <el-input
          placeholder="请输入内容"
          style="width: 50%; margin-left: 7%"
        ></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 2%">
      <el-col :span="12">
        <div class="dialog-label test-score-label"></div>
        <el-input
          placeholder="请输入内容"
          style="width: 50%; margin-left: 7%"
        ></el-input>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button>取消</el-button>
        <el-button> 保存 </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 编辑 -->

  <el-dialog
    title="编辑"
    :model-value="updateVisible"
    :before-close="updateClose"
    width="40%"
    height="90%"
  >
    <el-form :model="updateForm" label-width="120px">
      <el-form-item label="正确答案">
        <el-input v-model="updateForm.reference" style="width: 30%" />
      </el-form-item>
      <el-form-item label="分数">
        <el-input
          v-model="updateForm.score"
          style="width: 30%"
          type="number"
          :min="0"
          :max="1"
          step="0.01"
          placeholder="请输入0-1之间的数字"
          @input="validateUpdateFormScore"
        />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="updateForm.remark" style="width: 30%" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="updateClose">取消</el-button>
        <el-button @click="saveBut"> 保存 </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 详情detail -->

  <el-dialog
    title="详情"
    :model-value="detailVisible02"
    :before-close="detailClose"
    width="50%"
    height="100%"
  >
    <el-descriptions class="margin-top" :column="2" border>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">用例集ID</div>
        </template>
        {{ detailData.id }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">用例ID</div>
        </template>
        {{ detailData.test_case_id }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">版本</div>
        </template>
        {{ detailData.version }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">用户输入</div>
        </template>
        {{ detailData.user_input }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">答案</div>
        </template>
        {{ detailData.response }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">正确答案</div>
        </template>
        {{ detailData.reference }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">得分</div>
        </template>
        {{ detailData.score }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">创建人</div>
        </template>
        {{ detailData.executor }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">备注</div>
        </template>
        {{ detailData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>

  <!-- rag详情 -->

  <!-- 详情detail -->
  <el-dialog
    title="详情"
    :model-value="ragdetailVisible"
    :before-close="() => (ragdetailVisible = false)"
    width="70%"
    height="90%"
  >
    <!-- 任务基础信息 -->
    <el-card title="任务执行信息" class="mb-4">
      <el-descriptions border :column="3">
        <el-descriptions-item label="任务ID">{{
          ragDetailData.id
        }}</el-descriptions-item>
        <el-descriptions-item label="项目名称">{{
          ragDetailData.project_name
        }}</el-descriptions-item>
        <el-descriptions-item label="需求名称">{{
          ragDetailData.requirement_name
        }}</el-descriptions-item>
        <el-descriptions-item label="用例集名称">{{
          ragDetailData.suite_id
        }}</el-descriptions-item>
        <el-descriptions-item label="版本">{{
          ragDetailData.version
        }}</el-descriptions-item>

        <el-descriptions-item label="创建时间">
          {{ dayjs(ragDetailData.created_time).format("YYYY-MM-DD HH:mm:ss") }}
        </el-descriptions-item>

        <el-descriptions-item label="耗时（秒）">{{
          ragDetailData.execution_times
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 循环展示多个评估结果 -->
    <el-card title="指标" class="mb-4">
      <el-descriptions border :column="3">
        <el-descriptions-item label="上下文精准度">{{
          ragDetailData.context_precision
        }}</el-descriptions-item>
        <el-descriptions-item label="上下文召回">{{
          ragDetailData.context_recall
        }}</el-descriptions-item>
        <el-descriptions-item label=" 实体召回">{{
          ragDetailData.context_entity_recall
        }}</el-descriptions-item>
        <el-descriptions-item label="忠诚度">{{
          ragDetailData.faithfulness
        }}</el-descriptions-item>
        <el-descriptions-item label="相关性">{{
          ragDetailData.answer_relevancy
        }}</el-descriptions-item>
        <el-descriptions-item label="分数">{{
          ragDetailData.score
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card title="内容" class="mb-4">
      <el-descriptions border :column="3">
        <el-descriptions-item label="用户输入">{{
          ragDetailData.user_input
        }}</el-descriptions-item>
        <el-descriptions-item label="上下文">{{
          ragDetailData.retrieved_contexts
        }}</el-descriptions-item>
        <el-descriptions-item label=" 回答">{{
          ragDetailData.response
        }}</el-descriptions-item>
        <el-descriptions-item label="正确答案">{{
          ragDetailData.reference
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </el-dialog>

  <!-- llm详情 llmdetailVisible -->

  <el-dialog
    title="详情"
    :model-value="llmdetailVisible"
    :before-close="() => (llmdetailVisible = false)"
    width="70%"
    height="90%"
  >
    <!-- 任务基础信息 -->
    <el-card title="任务执行信息" class="mb-4">
      <el-descriptions border :column="3">
        <el-descriptions-item label="任务ID">{{
          llmDetailData.id
        }}</el-descriptions-item>
        <el-descriptions-item label="项目名称">{{
          llmDetailData.project_name
        }}</el-descriptions-item>
        <el-descriptions-item label="需求名称">{{
          llmDetailData.requirement_name
        }}</el-descriptions-item>
        <el-descriptions-item label="用例集名称">{{
          llmDetailData.suite_name
        }}</el-descriptions-item>
        <el-descriptions-item label="版本">{{
          llmDetailData.version
        }}</el-descriptions-item>

        <el-descriptions-item label="创建时间">
          {{ dayjs(llmDetailData.created_time).format("YYYY-MM-DD HH:mm:ss") }}
        </el-descriptions-item>

        <el-descriptions-item label="耗时（秒）">{{
          llmDetailData.execution_times
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 循环展示多个评估结果 -->
    <el-card title="指标" class="mb-4">
      <el-descriptions border :column="3">
        <el-descriptions-item label="AI回答的F1">{{
          llmDetailData.f1
        }}</el-descriptions-item>
        <el-descriptions-item label="AI回答的召回率">{{
          llmDetailData.recall
        }}</el-descriptions-item>
        <el-descriptions-item label="AI回答的准确性">{{
          llmDetailData.precision
        }}</el-descriptions-item>
        <el-descriptions-item label="AI推断是否满足用户">{{
          llmDetailData.agent_goal_accuracy
        }}</el-descriptions-item>
        <el-descriptions-item label="分数">{{
          llmDetailData.score
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card title="内容" class="mb-4">
      <el-descriptions border :column="3">
        <el-descriptions-item label="主题">{{
          llmDetailData.reference_topics
        }}</el-descriptions-item>
        <el-descriptions-item label="对话内容">{{
          llmDetailData.conversation
        }}</el-descriptions-item>
        <el-descriptions-item label="正确答案">{{
          llmDetailData.reference
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </el-dialog>
</template>

<script setup>
import { requireList } from "@/api/requireApi";
import { suiteInProject, suiteCase } from "@/api/suitApi";
import { ElMessage } from "element-plus";
import { serviceList } from "@/api/projectApi";
import { onMounted, ref, defineProps, watch, reactive, watchEffect } from "vue";
import { ragList, getEvaluateDetails } from "@/api/rag";
import dayjs from "dayjs";
import { llmList } from "@/api/llm";
import { llmListDetail } from "@/api/llm";
import {
  humanevaluatorList,
  addManualDate,
  detailManualDate,
  updateManualDate,
  updateRagDate,
} from "@/api/humanevaluator";
import { registerLoading } from "echarts";
// import { useStore } from 'vuex';

let dialogVisible = ref(false);
let suiteValue = ref();
// const store = useStore();
let requireListOptions = ref([]);
const activeName01 = ref("first");
const frameName = ref("frameEvaluate");
let serviceListTotal = ref(0);
let ragListTotal = ref(0);
let ragPage = ref(1);
let llmListTotal = ref(0);
let llmPage = ref(1);

let ragListParam = ref({
  projectgetid: null,
  suitValue: "",
  suitoptions: [],
  caseoptions: [],
  caseValue: null,
  page: 1,
  size: 10,
  type: "RAGAS",
  version: "",
  suit_id: null,
  test_case_id: null,
  keyword: null,
  project_id: null,
  metricValue: ref("context_precision"),
  scoreValue: ref("0.6以下"),
  metric: [
    {
      value: "context_precision",
      label: "上下文查准率",
    },
    {
      value: "context_recall",
      label: "上下文召回率",
    },
    {
      value: "context_entity_recall",
      label: "上下文实体召回率",
    },
    {
      value: "faithfulness",
      label: "忠实度",
    },
    {
      value: "answer_relevancy",
      label: "答案相关性",
    },
  ],

  score_range: [
    {
      value: "0.6以下",
      label: "0.6以下",
    },
    {
      value: "0.7-0.8",
      label: "0.7-0.8",
    },
    {
      value: "0.8-0.9",
      label: "0.8-0.9",
    },
    {
      value: "0.9以上",
      label: "0.9以上",
    },
  ],
});


let llmListParam = ref({
  projectgetid: null,
  suitValue: "",
  suitoptions: [],
  caseoptions: [],
  caseValue: null,
  page: 1,
  size: 10,
  type: "LLM",
  version: "",
  suit_id: null,
  test_case_id: null,
  keyword: null,
  project_id: null,
  metricValue: ref("agent_goal_accuracy"),
  scoreValue: ref("0.6以下"),
  metric: [
{
    value: "agent_goal_accuracy",
    label: "agent_goal_accuracy",
  },
  {
    value: "precision",
    label: "precision",
  },
  {
    value: "recall",
    label: "recall",
  },
  {
    value: "f1",
    label: "f1",
  },
  ],

  score_range: [
    {
      value: "0.6以下",
      label: "0.6以下",
    },
    {
      value: "0.7-0.8",
      label: "0.7-0.8",
    },
    {
      value: "0.8-0.9",
      label: "0.8-0.9",
    },
    {
      value: "0.9以上",
      label: "0.9以上",
    },
  ],
});
let ragDetailData = ref([]);
const props = defineProps({
  message: {
    type: String,
  },
  suite_id: {
    type: Number,
  },
  structureData: {
    type: Array,
  },
});
let llmstructureValue=ref([]);
let structureValue = ref([]);
const props1 = ref({
  checkStrictly: true,
});

watchEffect(() => {
  try {
    const id = props.suite_id; // 这里被自动追踪为依赖
    testarglist(id);
    // const response = await fetch(`https://api.example.com/users/${id}`);
    // userData.value = await response.json();
  } catch (error) {
    console.error("请求失败:", error);
  }
});
let updateForm = ref({
  reference: "",
  remark: "",
  score: 0,
});

let updateVisible = ref(false);

let updateid = ref();
let detailVisible02 = ref(false);
let serviceListPage = ref(1);

let detailData = ref({
  id: 0,
  reference: "",
  remark: "",
  response: "",
  score: 0,
  suit_id: 0,
  test_case_id: 0,
  user_input: "",
  version: "",
  executor: "",
});

let ragdetailVisible = ref(false);
const score = ref("");

const optionsfs = [];

for (let i = 1; i < 11; i++) {
  optionsfs.push({
    value: i,
    label: i,
  });
}
let llmdetailVisible = ref(false);
let llmDetailData = ref([]);
let suiteOptions = ref([]);

const treeProps = {
  label: "label", // 指定节点显示的文本字段
  children: "children", // 指定子节点字段
  value: "value", // 指定节点值字段
  isLeaf: "isLeaf",
};

async function getserviceList() {
  const params = { page: 1, page_size: 100 };
  const res = await serviceList(params);
  const projectNodes = res.resp.map((item) => ({
    value: item.id,
    label: item.name,
    children: [], // 初始化为空数组，懒加载时填充
  }));
  suiteOptions.value = projectNodes; // 更新根节点数据
  return projectNodes;
}

async function getSuiteid(projectId) {
  const params = { project_id: projectId, page: 1, page_size: 100 };
  const res = await suiteInProject(params);
  if (res.resp.length === 0) {
    ElMessage.warning("当前项目下暂无用例集，请选择其他项目查询！");
    return [];
  }
  const suiteNodes = res.resp.map((item) => ({
    value: item.id,
    label: item.name,
    children: [], // 初始化为空数组，懒加载时填充
    isLeaf: true,
  }));
  return suiteNodes;
}

// 重写数据加载逻辑（适配el-tree-select的懒加载）
const loadNode = (node, resolve) => {
  // node.level 表示节点深度（0-根节点，1-一级子节点，2-二级子节点）
  if (node.level === 0) {
    // 加载第一级（项目数据）
    getserviceList().then(() => {
      resolve(suiteOptions.value); // 将项目数据作为根节点
    });
  } else if (node.level === 1) {
    // 加载第二级（套件数据，node.data是当前点击的项目节点）
    getSuiteid(node.data.value).then((suiteItems) => {
      node.data.children = suiteItems; // 将套件数据作为项目节点的子节点
      resolve(suiteItems);
    });
  }
};

function llmHandleDeatil(val) {
  llmListDetail(val.id).then((res) => {
    if (res.success) {
      llmdetailVisible.value = true;
      llmDetailData.value = res.resp;
    }
  });
}

function handleDeatil(val) {
  getEvaluateDetails(val.id).then((res) => {
    if (res.success) {
      ragdetailVisible.value = true;
      ragDetailData.value = res.resp;
    }
  });
}

const handleClick = (tab, event) => {
  if (tab.paneName === "humaneEvaluate") {
    humanList();
  }
};

function detailClose() {
  detailVisible02.value = false;
}
function detail(val) {
  detailManualDate(val.id).then((res) => {
    detailData.value = Object.assign({}, res.resp);
  });
  detailVisible02.value = true;
}
// 验证分数输入
function validateScore(row) {
  let score = row.score;

  // 如果输入为空，直接返回
  if (score === '' || score === null || score === undefined) {
    return;
  }

  // 转换为数字
  let numScore = parseFloat(score);

  // 检查是否为有效数字
  if (isNaN(numScore)) {
    ElMessage.warning('分数必须为数字');
    row.score = '';
    return;
  }

  // 检查范围
  if (numScore < 0) {
    ElMessage.warning('分数不能为负数');
    row.score = 0;
    return;
  }

  if (numScore > 1) {
    ElMessage.warning('分数不能大于1');
    row.score = 1;
    return;
  }

  // 保留两位小数
  row.score = Math.round(numScore * 100) / 100;
}

// 验证更新表单中的分数输入
function validateUpdateFormScore() {
  let score = updateForm.value.score;

  // 如果输入为空，直接返回
  if (score === '' || score === null || score === undefined) {
    return;
  }

  // 转换为数字
  let numScore = parseFloat(score);

  // 检查是否为有效数字
  if (isNaN(numScore)) {
    ElMessage.warning('分数必须为数字');
    updateForm.value.score = '';
    return;
  }

  // 检查范围
  if (numScore < 0) {
    ElMessage.warning('分数不能为负数');
    updateForm.value.score = 0;
    return;
  }

  if (numScore > 1) {
    ElMessage.warning('分数不能大于1');
    updateForm.value.score = 1;
    return;
  }

  // 保留两位小数
  updateForm.value.score = Math.round(numScore * 100) / 100;
}

function changefs(val) {
  let data = {
    remark: val.remark,
    score: val.score,
  };
  updateRagDate(val.id, data).then((res) => {
    if (res.success) {
      testarglist()
      //humanList();
    }
  });
}
let manualVersion = ref();
let manualKey = ref();
function humanList() {
  let params = {
    page: serviceListPage.value,
    size: 10,
    version: manualVersion.value,
    suite_id: suiteValue.value,
    keyword: manualKey.value,
  };
  humanevaluatorList({ ...params }).then((res) => {
    if (res.success) {
      serviceListTotal.value = res.total;

      Case.value = res.resp.map((item) => {
        return {
          ...item,
          created_time: dayjs(item.created_time).format("YYYY-MM-DD HH:mm:ss"),
          updated_time: dayjs(item.updated_time).format("YYYY-MM-DD HH:mm:ss"),
        };
      });
    } else {
      ElMessage.error(res.msg);
      return (Case.value = res.resp);
    }
  });
}

function reset() {
  (manualVersion.value = null),
    (suiteValue.value = null),
    (manualKey.value = null),
    humanList();
}

function handlerun(val) {
  updateVisible.value = true;

  updateid.value = val.id;

  updateForm.value = Object.assign({}, val);
}
function updateClose() {
  updateVisible.value = false;
}

function handleCurrentChange01(val) {
  serviceListPage.value = val;
  humanList();
}

// RAG分页处理函数
function handleRagPageChange(val) {
  ragPage.value = val;
  ragListParam.value.page = val;
  testarglist();
}

// LLM分页处理函数
function handleLlmPageChange(val) {
  llmPage.value = val;
  llmListParam.value.page = val;
  llmquery();
}

// RAG重置函数
function resetRagForm() {
  // 重置RAG表单数据
  ragListParam.value = {
    projectgetid: null,
    suitValue: "",
    suitoptions: [],
    caseoptions: [],
    caseValue: null,
    page: 1,
    size: 10,
    type: "RAGAS",
    version: "",
    suit_id: null,
    test_case_id: null,
    keyword: null,
    project_id: null,
    metricValue: ref("context_precision"),
    scoreValue: ref("0.6以下"),
    metric: ragListParam.value.metric,
    score_range: ragListParam.value.score_range,
  };

  // 重置级联选择器
  structureValue.value = [];

  // 重置分页
  ragPage.value = 1;

  // 清空数据
  autoEvaluationData.value = [];
  ragListTotal.value = 0;

  ElMessage.success('RAG表单已重置');
}

// LLM重置函数
function resetLlmForm() {
  // 重置LLM表单数据
  llmListParam.value = {
    projectgetid: null,
    suitValue: "",
    suitoptions: [],
    caseoptions: [],
    caseValue: null,
    page: 1,
    size: 10,
    type: "LLM",
    version: "",
    suit_id: null,
    test_case_id: null,
    keyword: null,
    project_id: null,
    metricValue: ref("agent_goal_accuracy"),
    scoreValue: ref("0.6以下"),
    metric: llmListParam.value.metric,
    score_range: llmListParam.value.score_range,
  };

  // 重置级联选择器
  llmstructureValue.value = [];

  // 重置分页
  llmPage.value = 1;

  // 清空数据
  autoEvaluationData2.value = [];
  llmListTotal.value = 0;

  ElMessage.success('LLM表单已重置');
}
function llmquery(){
  let params = {
    page: llmPage.value,
    size: llmListParam.value.size,
    type_id:2,
    project_id:llmstructureValue.value[0]?llmstructureValue.value[0]:null,
    requirement_id:llmstructureValue.value[1]?llmstructureValue.value[1]:null,
    suite_id:llmstructureValue.value[2]?llmstructureValue.value[2]:null,
   version:llmListParam.value.version?llmListParam.value.version:null,
keyword:llmListParam.value.keyword?llmListParam.value.keyword:null,
metric:llmListParam.value.metricValue,
score_range:llmListParam.value.scoreValue


  };
   llmList({ ...params }).then((res) => {
      if (res.success) {
        autoEvaluationData2.value = res.resp.map((item) => {
          return {
            ...item,
            created_time: dayjs(item.created_time).format("YYYY-MM-DD HH:mm:ss"),
            // update_time: dayjs(item.update_time).format("YYYY-MM-DD HH:mm:ss"),
          };
        });

        // 设置LLM总数
        llmListTotal.value = res.total || 0;
      }
    });
}
const handleClick01 = (tab, event) => {
  if (tab.paneName === "second") {
    let params = {
      page: 1,
      size: 10,
    };
    llmList({ ...params }).then((res) => {
      if (res.success) {
        autoEvaluationData2.value = res.resp.map((item) => {
          return {
            ...item,
            created_time: dayjs(item.create_time).format("YYYY-MM-DD HH:mm:ss"),
            update_time: dayjs(item.update_time).format("YYYY-MM-DD HH:mm:ss"),
          };
        });
      }
    });
  }
  // res.total

  // res.total_page
};

let Case = ref([]);

let value01 = ref(1);
const options01 = [
  {
    value: 1,
    label: "RAG",
  },
  {
    value: 2,
    label: "LLM",
  },
];

function handleClose() {
  //detailVisible.value = false;
}

// 动态表头数据
let autoEvaluationColumns = ref([
  {
    prop: "project_name",
    label: "项目名称",
  },
  {
    prop: "version",
    label: "需求版本",
  },
  {
    prop: "requirement_name",
    label: "需求名称",
  },
  {
    prop: "suite_name",
    label: "用例集",
  },

  {
    prop: "user_input",
    label: "用户提问",
  },
  {
    prop: "retrieved_contexts",
    label: "检索上下文",
  },
  {
    prop: "response",
    label: "生成答案",
  },
  {
    prop: "reference",
    label: "标准答案",
  },
  {
    prop: "context_precision",
    label: "上下文精准度",
  },
  {
    prop: "faithfulness",
    label: "忠诚度",
  },
  {
    prop: "answer_relevancy",
    label: "答案准确性",
  },
  {
    prop: "context_entity_recall",
    label: "上下文召回度",
  },
  {
    prop: "executor",
    label: "执行人",
  },
  {
    prop: "created_time",
    label: "创建时间",
  },
]);

let autoEvaluationColumns2 = ref([
  {
    prop: "id",
    label: "ID",
  },
  {
    prop: "project_name",
    label: "项目名称",
  },
  {
    prop: "requirement_name",
    label: "需求名称",
  },
  {
    prop: "suite_name",
    label: "用例集名称",
  },

  {
    prop: "version",
    label: "版本",
  },
  {
    prop: "conversation",
    label: "对话内容",
  },
  {
    prop: "reference",
    label: "正确答案",
  },
  {
    prop: "reference_topics",
    label: "主题",
  },
  {
    prop: "agent_goal_accuracy",
    label: "是否满足目标",
  },
  {
    prop: "f1",
    label: "AI回答F1",
  },
  {
    prop: "precision",
    label: "AI回答准确度",
  },
  {
    prop: "score",
    label: "分数",
  },
  { prop: "recall", label: "AI回答的召回率" },

  {
    prop: "executor",
    label: "执行人",
  },

  {
    prop: "created_time",
    label: "创建时间",
  },
]);

let autoEvaluationColumns3 = ref([
  {
    prop: "id",
    label: "ID",
  },
  {
    prop: "user_input",
    label: "用户输入",
  },
  {
    prop: "reference",
    label: "标准答案",
  },

  { prop: "response", label: "回答" },

  { prop: "score", label: "得分" },
  {
    prop: "executor",
    label: "执行人",
  },
  {
    prop: "remark",
    label: "备注",
  },
  {
    prop: "created_time",
    label: "创建时间",
  },
  {
    prop: "updated_time",
    label: "更新时间1",
  },
]);

// 框架自动评估数据
let autoEvaluationData = ref([]);

let autoEvaluationData2 = ref([]);

function saveBut() {
  let data = {
    reference: updateForm.value.reference,
    remark: updateForm.value.remark,
    score: updateForm.value.score,
  };

  updateManualDate(updateid.value, data).then((res) => {
    if (res.success) {
      humanList();
      updateVisible.value = false;
    }
  });
  //updateDate
}
function changeProjectId() {
  requestServiceList(ragListParam.value.project_id);
}
function requestServiceList(val) {

  //请求项目列表接口
  let params = {
    page: 1,
    page_size: 100,
  };
  serviceList(params).then((res) => {
    res.resp.map((item) => {
      console.log(item.name);

      if (val === item.name) {

        return (ragListParam.value.projectgetid = item.id);
      }
      // return {

      // }
    });
  });
}

//获取用例集
async function requestSuitList() {
  // 改为async函数
  try {
    const res = await suiteInProject({
      project_id: ragListParam.value.projectgetid,
    }); // 使用await等待接口响应

    if (res.success) {
      ragListParam.value.suitoptions = res.resp.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    } else {
      // 接口返回失败时提示
      ElMessage.error({
        message: "获取用例集失败：请先选择项目",
        type: "error",
        center: true,
      });
    }
  } catch (error) {
    // 统一捕获网络错误和同步错误
    // 网络异常或其他错误时提示
    ElMessage.error({
      message: `获取用例集失败：请先选择项目`,
      type: "error",
      center: true,
    });
  }
}
function requestcaseList(val) {
  // console.log('structureValue',structureValue.value[2]);

    if (val !== null && val !== undefined) {
     suiteCase({ suite_id: val }).then((res) => {
    ragListParam.value.caseoptions = res.resp.map((item) => ({
      label: item.content,
      value: item.id,
    }));
  });
  }else{
     ElMessage.error({
      message: `请先选择用例集！`,
      type: "error",
      center: true,
    });
  }


}
function getrequireList() {
  requireList(ragListParam.value.projectgetid).then((res) => {
    requireListOptions.value = res.resp.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  });
}

// 接口请求
function testarglist(val) {
  let params = {
    page: ragPage.value,
    size: ragListParam.value.size,
    type_id: 1,
    version: ragListParam.value.version,
    requirement_id: structureValue.value[1] ? structureValue.value[1] : null,
    suit_id: structureValue.value[2] ? structureValue.value[2] : null,
    material_id: ragListParam.value.test_case_id,
    keyword: ragListParam.value.keyword,
    project_id: structureValue.value[0] ? structureValue.value[0] : null,
    metric: ragListParam.value.metricValue,
    score_range: ragListParam.value.scoreValue,
  };

  ragList(params).then((res) => {
    if (res.success) {
      autoEvaluationData.value = res.resp.map((item) => {
        return {
          ...item,
          created_time: dayjs(item.created_time).format("YYYY-MM-DD HH:mm:ss"),
          // update_time: dayjs(item.update_time).format("YYYY-MM-DD HH:mm:ss"),
        };
      });

      ragListTotal.value = res.total;
    }
    // res.total

    // res.total_page
  });
}

onMounted(() => {
  testarglist();
  // console.log('message======',store.state.testData)
});
</script>
<style scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
.dialog-footer button:first-child {
  margin-right: 10px;
}

/* 查询项标签样式 */
.query-label {
  display: inline-block;
  font-size: 14px;
  margin-right: 8px;
  margin-left: 16px;
  color: #606266;
  font-weight: 500;
}

.query-label:first-child {
  margin-left: 0;
}



/* 对话框标签样式 */
.dialog-label {
  display: inline-block;
  font-size: 14px;
  margin-right: 8px;
  color: #606266;
  font-weight: 500;
}



/* 分数输入框样式 */
.el-input__inner[type="number"] {
  text-align: center;
}

/* 隐藏数字输入框的上下箭头 */
.el-input__inner::-webkit-outer-spin-button,
.el-input__inner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.el-input__inner[type="number"] {
  -moz-appearance: textfield;
}
</style>
