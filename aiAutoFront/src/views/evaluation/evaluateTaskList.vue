<template>
  <div class="execution-list-container">
    <!-- 搜索过滤区域 -->
    <div class="filter-bar">
      <!-- <el-select
      v-model="suite_sequence_id"
      filterable
      reserve-keyword
      @focus="getSuiteExecute(counter.get_rag_suite_id)"
      @blur="get1111()"
    >
      <el-option
        v-for="item in counter.SequenceData"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      <span style="float: left">{{'序列号'}}{{ item.num }}{{"："}}{{item.label}}</span>
      </el-option>
    </el-select> -->

      <el-tree-select
        v-model="store.suiteValue"
        :props="treeProps"
        :load="loadNode"
        lazy
        style="width: 200px"
        placeholder="请选择层级数据"
      />

      <el-select
        v-model="store.filterStatusvalue"
        placeholder="状态筛选"
        style="width: 200px"
      >
        <el-option label="待执行" value="1" />
        <el-option label="执行中" value="2" />
        <el-option label="成功" value="3" />
        <el-option label="失败" value="4" />
        <el-option label="超时" value="5" />
      </el-select>

      <el-button type="primary" @click="store.fetchTaskList()">查询</el-button>
    </div>

    <!-- 主表格区域 -->
    <el-table :data="filteredList" style="width: 100%">
      <!-- 基础信息列 -->
      <el-table-column
        prop="id"
        label="ID"
        min-width="8%"
        sortable
        show-overflow-tooltip
      />
      <el-table-column
        label="用例集ID"
        prop="suite_id"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="用例集名称"
        prop="suite_name"
        min-width="15%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="用例集执行ID"
        prop="original_request.suite_sequence_id"
        min-width="12%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="评估耗时"
        prop="execution_times"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="状态"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
        <template #default="scope">
          <el-tag :type="getTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="执行开始时间"
        prop="start_time"
        min-width="15%"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatTime(row.start_time) }}
        </template>
      </el-table-column>

      <el-table-column
        label="执行结束时间"
        prop="end_time"
        min-width="15%"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatTime(row.end_time) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="140" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              link
              @click.stop="viewDetail(row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button
              type="danger"
              size="small"
              link
              @click.stop="store.postragRetry(row)"
              v-if="row.status === 4 || row.status === 5"
            >
              <el-icon><Refresh /></el-icon>
              重试
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-footer style="float: right; background: #fff; width: 100%">
      <el-pagination
        :page-size="20"
        :pager-count="11"
        layout="prev, pager, next"
        :total="store.TaskListTotal"
        @current-change="store.handleCurrentChange"
        style="float: right; margin-top: 0.8%"
      />
    </el-footer>

    <!-- 在表格操作列下方添加 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="任务详情"
      width="80%"
      :before-close="() => (detailDialogVisible = false)"
    >
        <!-- 基础信息 -->
        <el-descriptions border :column="3" style="margin-bottom: 20px">
          <el-descriptions-item label="任务ID">{{
            currentDetail?.id
          }}</el-descriptions-item>
          <el-descriptions-item label="用例集ID">{{
            currentDetail?.suite_id
          }}</el-descriptions-item>
          <el-descriptions-item label="用例集名称">{{
            currentDetail?.suite_name
          }}</el-descriptions-item>
          <el-descriptions-item label="用例集执行ID">{{
            currentDetail?.original_request?.suite_sequence_id
          }}</el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag :type="getTagType(currentDetail?.status)">
              {{ getStatusText(currentDetail?.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="评估耗时">{{
            currentDetail?.execution_times || '未知'
          }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{
            formatTime(currentDetail?.start_time)
          }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{
            formatTime(currentDetail?.end_time)
          }}</el-descriptions-item>
          <el-descriptions-item
            label="错误信息"
            v-if="currentDetail?.error"
            :span="3"
          >
            <el-text type="danger">{{ currentDetail.error }}</el-text>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 评估结果详情 -->
        <el-card v-if="currentDetail?.results && currentDetail.results.length > 0" style="margin-bottom: 20px">
          <template #header>
            <div class="card-header">
              <span>评估结果详情</span>
              <el-tag type="info">共 {{ currentDetail.results.length }} 条结果</el-tag>
            </div>
          </template>

          <!-- 结果列表 -->
          <el-collapse v-model="activeResultIndex" accordion>
            <el-collapse-item
              v-for="(result, index) in currentDetail.results"
              :key="index"
              :title="`结果 ${index + 1}${result.reference_topics ? ' - ' + result.reference_topics : ''}`"
              :name="index"
            >
              <!-- 指标展示 -->
              <el-row :gutter="20" style="margin-bottom: 15px">
                <el-col :span="6" v-if="result.precision !== undefined">
                  <el-statistic title="精确度" :value="result.precision" :precision="3">
                    <template #suffix>
                      <el-tag :type="getScoreType(result.precision)" size="small">
                        {{ getScoreLevel(result.precision) }}
                      </el-tag>
                    </template>
                  </el-statistic>
                </el-col>
                <el-col :span="6" v-if="result.recall !== undefined">
                  <el-statistic title="召回率" :value="result.recall" :precision="3">
                    <template #suffix>
                      <el-tag :type="getScoreType(result.recall)" size="small">
                        {{ getScoreLevel(result.recall) }}
                      </el-tag>
                    </template>
                  </el-statistic>
                </el-col>
                <el-col :span="6" v-if="result.f1 !== undefined">
                  <el-statistic title="F1分数" :value="result.f1" :precision="3">
                    <template #suffix>
                      <el-tag :type="getScoreType(result.f1)" size="small">
                        {{ getScoreLevel(result.f1) }}
                      </el-tag>
                    </template>
                  </el-statistic>
                </el-col>
                <el-col :span="6" v-if="result.score !== undefined">
                  <el-statistic title="总分" :value="result.score" :precision="2">
                    <template #suffix>
                      <el-tag :type="getScoreType(result.score)" size="small">
                        {{ getScoreLevel(result.score) }}
                      </el-tag>
                    </template>
                  </el-statistic>
                </el-col>
              </el-row>

              <!-- 其他指标 -->
              <el-descriptions border :column="2" style="margin-bottom: 15px" v-if="hasOtherMetrics(result)">
                <el-descriptions-item label="版本" v-if="result.version">{{ result.version }}</el-descriptions-item>
                <el-descriptions-item label="主题" v-if="result.reference_topics">{{ result.reference_topics }}</el-descriptions-item>
                <el-descriptions-item label="目标准确性" v-if="result.agent_goal_accuracy !== undefined">
                  <el-tag :type="result.agent_goal_accuracy ? 'success' : 'danger'">
                    {{ result.agent_goal_accuracy ? '是' : '否' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="忠诚度" v-if="result.faithfulness !== undefined">{{ result.faithfulness?.toFixed(3) }}</el-descriptions-item>
                <el-descriptions-item label="答案相关性" v-if="result.answer_relevancy !== undefined">{{ result.answer_relevancy?.toFixed(3) }}</el-descriptions-item>
                <el-descriptions-item label="上下文精准度" v-if="result.context_precision !== undefined">{{ result.context_precision?.toFixed(3) }}</el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <!-- 内容详情 -->
        <el-card v-if="currentDetail?.results && currentDetail.results.length > 0">
          <template #header>
            <span>内容详情</span>
          </template>

          <el-tabs v-model="activeContentTab" >
            <el-tab-pane
              v-for="(result, index) in currentDetail.results"
              :key="index"
              :label="`结果 ${index + 1}`"
              :name="`result-${index}`"
            >
              <!-- 对话内容 -->
              <el-card v-if="result.conversation && result.conversation.length > 0" class="mb-4">
                <template #header>
                  <span>对话内容</span>
                </template>
                <div class="conversation-container">
                  <div
                    v-for="(conv, convIndex) in result.conversation"
                    :key="convIndex"
                    class="conversation-item"
                    :class="conv.role"
                  >
                    <div class="role-label">
                      <el-tag :type="conv.role === 'user' ? 'primary' : 'success'" size="small">
                        {{ conv.role === 'user' ? '用户' : '助手' }}
                      </el-tag>
                    </div>
                    <div class="content">
                      <pre>{{ conv.content }}</pre>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 参考内容与响应对比 -->
              <el-row :gutter="20">
                <el-col :span="12" v-if="result.reference">
                  <el-card>
                    <template #header>
                      <span>参考答案</span>
                    </template>
                    <div class="content-box reference-content">
                      <pre>{{ result.reference }}</pre>
                    </div>
                  </el-card>
                </el-col>
                <el-col :span="12" v-if="result.response">
                  <el-card>
                    <template #header>
                      <span>AI响应</span>
                    </template>
                    <div class="content-box response-content">
                      <pre>{{ result.response }}</pre>
                    </div>
                  </el-card>
                </el-col>
              </el-row>

              <!-- 检索上下文 -->
              <el-card v-if="result.retrieved_contexts" class="mt-4">
                <template #header>
                  <span>检索上下文</span>
                </template>
                <div class="content-box context-content">
                  <pre>{{ result.retrieved_contexts }}</pre>
                </div>
              </el-card>

              <!-- 用户输入 -->
              <el-card v-if="result.user_input" class="mt-4">
                <template #header>
                  <span>用户输入</span>
                </template>
                <div class="content-box input-content">
                  <pre>{{ result.user_input }}</pre>
                </div>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-card>

    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useCounterStore } from "@/pinia/taskstore";
import { serviceList } from "@/api/projectApi";
import { suiteInProject, getResultSequence } from "@/api/suitApi";
import dayjs from "dayjs";
import { View, Refresh } from '@element-plus/icons-vue';
const store = useCounterStore();

// const props = defineProps({
//   data: {
//     type: Array,
//     required: true,
//   },
//   total: {
//     type: Number,
//     default: 0,
//   },
// });

const searchKey = ref("");
const filterStatus = ref([]);
const treeProps = {
  label: "label", // 指定节点显示的文本字段
  children: "children", // 指定子节点字段
  value: "value", // 指定节点值字段
  isLeaf: "isLeaf",
};

let suiteOptions = ref([]);

// 重写数据加载逻辑（适配el-tree-select的懒加载）
const loadNode = (node, resolve) => {
  // node.level 表示节点深度（0-根节点，1-一级子节点，2-二级子节点）
  if (node.level === 0) {
    // 加载第一级（项目数据）
    getserviceList().then(() => {
      resolve(suiteOptions.value); // 将项目数据作为根节点
    });
  } else if (node.level === 1) {
    // 加载第二级（套件数据，node.data是当前点击的项目节点）
    getSuiteid(node.data.value).then((suiteItems) => {
      node.data.children = suiteItems; // 将套件数据作为项目节点的子节点
      resolve(suiteItems);
    });
  } else if (node.level === 2) {
    // 加载第三级（序列号数据，node.data是当前点击的套件节点）
    getSuiteExecute(node.data.value).then((sequenceItems) => {
      node.data.children = sequenceItems; // 将序列号数据作为套件节点的子节点
      resolve(sequenceItems);
    });
  }
};

// 修改原有数据加载函数为Promise形式（适配async/await）
async function getserviceList() {
  const params = { page: 1, page_size: 100 };
  const res = await serviceList(params);
  const projectNodes = res.resp.map((item) => ({
    value: item.id,
    label: item.name,
    children: [], // 初始化为空数组，懒加载时填充
  }));
  suiteOptions.value = projectNodes; // 更新根节点数据
  return projectNodes;
}

async function getSuiteid(projectId) {
  const params = { project_id: projectId, page: 1, page_size: 100 };
  const res = await suiteInProject(params);
  const suiteNodes = res.resp.map((item) => ({
    value: item.id,
    label: item.name,
    children: [], // 初始化为空数组，懒加载时填充
  }));
  return suiteNodes;
}

async function getSuiteExecute(suiteId) {
  const params = { suite_id: suiteId };
  const res = await getResultSequence(params);
  const sequenceNodes = res.resp.map((item) => ({
    value: item.id,
    label: `序列号${item.sequence_num}：${item.suite__name}`,
    isLeaf: true,
    // 叶节点无需children（或设为undefined）
  }));
  return sequenceNodes;
}

// 格式化时间显示
const formatTime = (time) => {
  return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
};

// 评分类型判断
const getScoreType = (score) => {
  if (score >= 0.8) return "success";
  if (score >= 0.5) return "warning";
  return "danger";
};

// 评分等级判断
const getScoreLevel = (score) => {
  if (score >= 0.9) return "优秀";
  if (score >= 0.8) return "良好";
  if (score >= 0.6) return "一般";
  if (score >= 0.4) return "较差";
  return "很差";
};

// 检查是否有其他指标
const hasOtherMetrics = (result) => {
  return result.version || result.reference_topics ||
         result.agent_goal_accuracy !== undefined ||
         result.faithfulness !== undefined ||
         result.answer_relevancy !== undefined ||
         result.context_precision !== undefined;
};

// 列表过滤计算
const filteredList = computed(() => {
  return store.taskListData.filter((item) => {
    const matchSearch =
      !searchKey.value ||
      item.test_case_name.includes(searchKey.value) ||
      item.id.toString().includes(searchKey.value);

    const matchStatus =
      filterStatus.value.length === 0 ||
      filterStatus.value.includes(item.status);

    return matchSearch && matchStatus;
  });
});

// ... 现有代码 ...

// 新增：详情对话框状态
const detailDialogVisible = ref(false);
const currentDetail = ref(null); // 存储当前选中的详情数据
const activeResultIndex = ref(0); // 当前展开的结果索引
const activeContentTab = ref('result-0'); // 当前激活的内容标签页

// 修改viewDetail方法，触发对话框显示
const viewDetail = (row) => {
  currentDetail.value = row; // 存储当前行数据
  activeResultIndex.value = 0; // 重置展开的结果索引
  activeContentTab.value = 'result-0'; // 重置内容标签页
  detailDialogVisible.value = true; // 显示对话框
};

// 执行状态文本映射

// 状态文本映射
const getStatusText = (status) => {
  const map = {
    success: "成功",
    failed: "失败",
    running: "执行中",
    pending: "待执行",
    timeout: "超时",
  };
  // 1-pending/2-running/3-completed/4-failed/5-timeout
  if (status === 1) {
    return map["pending"];
  } else if (status === 2) {
    return map["running"];
  } else if (status === 3) {
    return map["success"];
  } else if (status === 4) {
    return map["failed"];
  } else {
    return map["info"];
  }

  // return map[status] || '未知';
};

// 状态标签颜色映射
const getTagType = (status) => {
  const map = {
    completed: "success",
    failed: "danger",
    running: "primary",
    pending: "warning",
  };
  //return map[status] || 'info';
  if (status === 1) {
    return map["pending"];
  } else if (status === 2) {
    return map["running"];
  } else if (status === 3) {
    return map["success"];
  } else if (status === 4) {
    return map["failed"];
  } else {
    return map["info"];
  }
};

onMounted(async () => {
  await store.fetchTaskList();
});
</script>

<style scoped>
.execution-list-container {
  padding: 20px;
}

.filter-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.suite-info {
  display: flex;
  flex-direction: column;
}
.suite-id {
  color: #909399;
  font-size: 12px;
}
.suite-name {
  font-weight: 500;
}

.metric-card {
  margin-bottom: 10px;
}
.metric-title {
  font-weight: bold;
  margin-bottom: 8px;
}
.metric-values {
  display: flex;
  gap: 10px;
}
.metric-item {
  display: flex;
  align-items: center;
  gap: 5px;
}
.label {
  color: #606266;
}

.response-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  max-height: 400px;
  overflow-y: auto;
}
.response-item pre {
  white-space: pre-wrap;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}
.reference pre {
  background: #f0f9eb;
}

.error-panel {
  padding: 10px;
  background: #fef0f0;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 新增样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-container {
  max-height: 400px;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.conversation-item.user {
  background-color: #e3f2fd;
}

.conversation-item.assistant {
  background-color: #f1f8e9;
}

.role-label {
  margin-right: 15px;
  min-width: 60px;
}

.content {
  flex: 1;
}

.content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

.content-box {
  max-height: 300px;
  overflow-y: auto;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.reference-content {
  background-color: #f0f9eb;
  border-color: #b7eb8f;
}

.response-content {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.context-content {
  background-color: #fff7e6;
  border-color: #ffd591;
}

.input-content {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.content-box pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
