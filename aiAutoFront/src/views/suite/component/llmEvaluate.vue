<template>
  <!-- LLM评估弹窗 -->
  <el-dialog
    v-model="counter.llmTestShow"
    :beforeClose="handleLlmDialogClose"
    title="LLM评估"
    style="height: 50%; width: 40%"
  >
    <el-row :gutter="20">
      <el-col :span="10">
        <div v-if="counter.optionspg1">
          <el-form
            ref="llmFormRef"
            :label-position="labelPosition"
            label-width="100px"
            :model="counter.llmFormData"
            :rules="llmRules"
            style="max-width: 460px"
          >
            <el-form-item label="项目" prop="project_id">
              <el-select
                v-model="counter.llmFormData.project_id"
                filterable
                reserve-keyword
                placeholder="请选择项目"
                @focus="counter.getProjectId()"
                @change="onProjectChange"
                clearable
              >
                <el-option
                  v-for="item in counter.projectData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="用例集" prop="suite_id">
              <el-select
                v-model="counter.llmFormData.suite_id"
                filterable
                reserve-keyword
                placeholder="请选择用例集"
                @focus="counter.getSuiteid(counter.llmFormData.project_id)"
                @change="onSuiteChange"
                clearable
              >
                <el-option
                  v-for="item in counter.formLabelAlign3Options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="用例评估" prop="suite_sequence_id">
              <el-select
                v-model="counter.llmFormData.suite_sequence_id"
                filterable
                reserve-keyword
                placeholder="请选择用例评估"
                @focus="counter.getSuiteExecute(counter.llmFormData.suite_id)"

                clearable
              >
                <el-option
                  v-for="item in counter.SequenceData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left"
                    >{{ "序列号" }}{{ item.num }}{{ "："
                    }}{{ item.label }}</span
                  >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="模型名称" prop="model_id">
             <el-select
              v-model="counter.llmFormData.model_id"
              filterable
              reserve-keyword
              placeholder="请选择模型"
              @focus="counter.getllmConfig()"
              clearable
            >
              <el-option
                v-for="item in counter.modelData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            </el-form-item>
            <el-form-item label="向量模型" prop="type_id">
              <el-input
                v-model="counter.llmFormData.type_id"
                placeholder="请输入向量模型"
              />
            </el-form-item>

            <el-form-item>
              <el-button @click="handleLlmEvaluate" type="primary">LLM评估</el-button>
              <el-button @click="cancelLlm">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-form
          ref="llmManualFormRef"
          :label-position="labelPosition"
          label-width="100px"
          :model="counter.manualForm"
          :rules="llmManualRules"
          style="max-width: 460px"
          v-if="counter.rengongval"
        >
          <el-form-item label="项目" prop="project_id">
            <el-select
              v-model="counter.manualForm.project_id"
              filterable
              reserve-keyword
              placeholder="请选择项目"
              @focus="counter.getProjectId()"
              @change="onLlmManualProjectChange"
              clearable
            >
              <el-option
                v-for="item in counter.projectData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用例集" prop="suite_id">
            <el-select
              v-model="counter.manualForm.suite_id"
              filterable
              reserve-keyword
              @focus="counter.getSuiteid(counter.manualForm.project_id)"
              @change="onLlmManualSuiteChange"
              :placeholder="counter.suiteNamePlaceholder || '请选择用例集'"
              clearable
            >
              <el-option
                v-for="item in counter.formLabelAlign3Options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用例名称" prop="test_case_id">
            <el-select
              @focus="counter.materialDialog(counter.manualForm.suite_id)"
              v-model="counter.manualForm.test_case_id"
              filterable
              multiple
              :placeholder="counter.casePlaceholder || '请选择用例'"
              clearable
            >
              <el-option
                v-for="item in counter.caseOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="版本">
            <el-input v-model="counter.manualForm.version"   :placeholder="counter.caseVersionPlaceholder"/>
          </el-form-item> -->

          <el-form-item label="模型回答" prop="response">
            <el-input
              v-model="counter.manualForm.response"
              type="textarea"
              :rows="3"
              placeholder="请输入模型回答"
            />
          </el-form-item>
          <el-form-item label="得分" prop="score">
            <el-input-number
              v-model="counter.manualForm.score"
              :min="0"
              :max="100"
              :precision="2"
              placeholder="请输入得分"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="counter.manualForm.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入备注（可选）"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleLlmManualEvaluate" type="primary">提交评估</el-button>
            <el-button @click="cancelLlmHuman()">取消</el-button>
          </el-form-item>
        </el-form>
        <!-- </div> -->
      </el-col>
      <el-col :span="5" :offset="6">
        <el-switch
          v-model="counter.value90"
          size="small"
          active-text="是否开启人工评估"
          @change="counter.value90change"
        />
      </el-col>
    </el-row>
  </el-dialog>

  <!-- LLM评估结果弹窗 -->
  <el-dialog
    v-model="counter.llmcard"
    title="LLM评估结果"
    :before-close="() => (counter.llmcard = false)"
    style="width: 20%; background-color: #ebf6fe"
  >
    <div
      style="
        border: 5px solid #96bcd9;
        border-top-width: 0.5em;
        border-bottom-width: 0;
      "
    >
      <el-divider border-style="dashed" />
    </div>
    <el-card
      class="box-card"
      style="
        width: 97%;
        background-color: #f3f9fe;
        margin-top: -11%;
        margin-left: 1.5%;
      "
    >
      <div
        style="
          background-color: #f3f9fe;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <span>任务id</span>
        <p>{{ counter.llm_task_id }}</p>
      </div>

      <div
        style="
          background-color: #f3f9fe;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <span>评估状态</span>
        <el-tag :type="counter.getLlmStatusType(counter.llm_status)">
          {{ counter.getLlmStatusText(counter.llm_status) }}
        </el-tag>
      </div>
      <div
        style="
          background-color: #f3f9fe;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <span>开始时间</span>
        <p>{{ counter.llm_start_time}}</p>
      </div>
    </el-card>

    <template #footer>
      <span style="margin-right: 40%">
        <el-button
          style="margin-top: 10%"
          plain
          type="primary"
          round
          @click="counter.LlmEvaluateStatus(counter.llm_task_id)"
        >
          查看详情</el-button
        >
      </span>
    </template>
  </el-dialog>

  <!-- LLM评估详情页面 -->
  <el-dialog
    v-model="counter.llmStatusDialog"
    title="LLM评估详情页面"
    style="width: 80%; max-height: 90vh; overflow-y: auto"
  >
    <!-- 基础信息卡片 -->
    <el-descriptions border :column="4" title="基础信息">
      <el-descriptions-item label="评估状态">
        <el-tag :type="counter.getLlmStatusType(counter.llmStatus.status)">{{
          counter.getLlmStatusText(counter.llmStatus.status)
        }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="执行耗时"
        >{{ counter.llmStatus.duration_seconds }}/s</el-descriptions-item
      >
      <el-descriptions-item label="开始时间">{{
        counter.formatTime(counter.llmStatus.start_time)
      }}</el-descriptions-item>
      <el-descriptions-item label="结束时间">{{
        counter.formatTime(counter.llmStatus.end_time)
      }}</el-descriptions-item>
       <el-descriptions-item v-if='counter.llmStatus.status===4' label="错误信息">{{
        counter.llmStatus.error
      }}{{ counter.llmStatus.retry_suggestion }}
    </el-descriptions-item>
    </el-descriptions>

    <template #header>
      <div class="card-header">评估状态</div>
    </template>
    <el-table
      :data="counter.llmStatus.results"
      border
      style="width: 100%; margin-top: 16px"
      v-if="counter.llmStatus.results?.length"
    >
      <el-table-column label="用户输入" prop="label" min-width="30%">
        <template #default="{ row }">
          {{ row.user_input }}
        </template>
      </el-table-column>
      <el-table-column label="指标值" min-width="20%">
        <template #default="{ row }">
          <span v-for="i in row.material"
            >{{ i.label || "无" }}:{{ i.value || "无" }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="回答" prop="response" min-width="50%">
        <template #default="{ row }">
          <span style="white-space: pre-wrap">{{ row.response || "无" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="正确答案" prop="reference" min-width="50%">
        <template #default="{ row }">
          <span style="white-space: pre-wrap">{{ row.reference || "无" }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div v-else style="text-align: center; padding: 20px">
      无核心评估指标数据
    </div>

    <!-- 对话记录表格 -->
    <el-card v-if="counter.llmStatus.conversations?.length">
      <template #header>
        <div class="card-header">对话记录详情</div>
      </template>
      <el-table
        :data="counter.llmStatus.conversations"
        border
        style="width: 100%"
      >
        <el-table-column
          label="角色"
          prop="role"
          min-width="15%"
        ></el-table-column>
        <el-table-column label="对话内容" min-width="60%">
          <template #default="{ row }">
            <pre style="white-space: pre-wrap">{{ row.content }}</pre>
          </template>
        </el-table-column>
        <el-table-column label="时间" prop="timestamp" min-width="25%">
          <template #default="{ row }">
            {{ counter.formatTime(row.timestamp) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 错误信息提示（仅失败时显示） -->
    <el-alert
      v-if="counter.llmStatus.status === 'failed'"
      title="错误详情"
      type="error"
      :description="counter.llmStatus.error_message"
      show-icon
      style="margin-top: 20px"
    />
  </el-dialog>
</template>

<script setup>
import { suiteStore } from "@/pinia/suitstore";
import { ref, reactive, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";

const counter = suiteStore();
const labelPosition = ref("left");
const llmManualFormRef = ref();
const llmFormRef = ref();

// LLM评估01表单校验规则
const llmRules = reactive({
  project_id: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ],
  suite_id: [
    { required: true, message: '请选择用例集', trigger: 'change' }
  ],
  suite_sequence_id: [
    { required: true, message: '请选择用例评估', trigger: 'change' }
  ],
  model_id: [
    { required: true, message: '请选择模型名称', trigger: 'change' }
  ],
  type_id: [
    { required: true, message: '请输入向量模型', trigger: 'blur' },
    { min: 1, max: 100, message: '向量模型长度需在1-100字符之间', trigger: 'blur' }
  ]
});

// LLM人工评估表单校验规则
const llmManualRules = reactive({
  project_id: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ],
  suite_id: [
    { required: true, message: '请选择用例集', trigger: 'change' }
  ],
  test_case_id: [
    { required: true, message: '请选择用例名称', trigger: 'change' }
  ],
  response: [
    { required: true, message: '请输入模型回答', trigger: 'blur' },
    { min: 1, max: 2000, message: '模型回答长度需在1-2000字符之间', trigger: 'blur' }
  ],
  score: [
    { required: true, message: '请输入得分', trigger: 'change' },
    { type: 'number', min: 0, max: 100, message: '得分范围为0-100', trigger: 'change' }
  ]
});

// LLM评估01项目选择变化处理
function onProjectChange(projectId) {
  // 清空用例集选择
  counter.llmFormData.suite_id = null;
  counter.llmFormData.suite_sequence_id = null;
  // 重置用例集相关数据
  counter.formLabelAlign3Options = [];
  counter.SequenceData = [];
}

// LLM评估01用例集选择变化处理
function onSuiteChange(suiteId) {
  // 清空用例评估选择
  counter.llmFormData.suite_sequence_id = null;
  // 重置用例评估数据
  counter.SequenceData = [];
}

// LLM人工评估项目选择变化处理
function onLlmManualProjectChange(projectId) {
  // 清空用例集选择
  counter.manualForm.suite_id = null;
  counter.manualForm.test_case_id = null;
  // 重置用例集相关数据
  counter.formLabelAlign3Options = [];
  counter.caseOptions = [];
}

// LLM人工评估用例集选择变化处理
function onLlmManualSuiteChange(suiteId) {
  // 清空用例名称选择
  counter.manualForm.test_case_id = null;
  // 重置用例数据
  counter.caseOptions = [];
}

// LLM评估01提交处理
function handleLlmEvaluate() {
  if (!llmFormRef.value) return;

  llmFormRef.value.validate((valid) => {
    if (valid) {
      // 调用LLM评估接口

      counter.postllmEvaluate();
    } else {
      ElMessage.error('请完善表单信息');
      return false;
    }
  });
}

// LLM人工评估提交处理
function handleLlmManualEvaluate() {
  if (!llmManualFormRef.value) return;

  llmManualFormRef.value.validate((valid) => {
    if (valid) {
      // 调用评估接口
      counter.postManualDate(counter.manualForm);
    } else {
      ElMessage.error('请完善表单信息');
      return false;
    }
  });
}

let dialogpg = ref(false);
const dialogpinggu = ref(false);
function savepg() {
  dialogpg.value = true;
  dialogpinggu.value = false;
}

const checked4 = ref(false);
function llmpg() {
  counter.llmcard = true;
  counter.llmTestShow = false;
  checked4.value = false;
  counter.postllmEvaluate();
}

function cancelLlm() {
  // 清除LLM评估01表单验证状态
  if (llmFormRef.value) {
    llmFormRef.value.clearValidate();
  }
  // 清除LLM表单数据
  counter.llmFormData = {
    type_id: null,
    model_id: null,
    suite_sequence_id: null,
    project_id: null,
    suite_id: null,
  };
  // 清除相关选项数据
  counter.formLabelAlign3Options = [];
  counter.SequenceData = [];
  // 重置人工评估开关状态
  counter.value90 = false;
  counter.optionspg1 = true;
  counter.rengongval = false;
  counter.llmTestShow = false;
}

function cancelLlmHuman() {
  // 清除表单验证状态
  if (llmManualFormRef.value) {
    llmManualFormRef.value.clearValidate();
  }
  // 清除人工评估表单数据
  counter.manualForm = {
    project_id: null,
    suite_id: null,
    test_case_id: null,
    response: '',
    score: null,
    remark: ''
  };
  // 清除相关选项数据
  counter.caseOptions = [];
  // 重置人工评估开关状态
  counter.value90 = false;
  counter.optionspg1 = true;
  counter.rengongval = false;
  counter.llmTestShow = false;
}

// 监听对话框打开状态，清空验证规则和数据
watch(() => counter.llmTestShow, (newVal) => {
  if (newVal) {
    // 对话框打开时，清空表单验证状态和数据
    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
      if (llmFormRef.value) {
        llmFormRef.value.clearValidate();
      }
      if (llmManualFormRef.value) {
        llmManualFormRef.value.clearValidate();
      }
    });
  }
});

// 监听人工评估开关状态，清空验证规则
watch(() => counter.value90, (newVal) => {
  // 无论开启还是关闭人工评估，都清空相关表单的验证状态
  nextTick(() => {
    if (newVal) {
      // 开启人工评估时，清空人工评估表单验证
      if (llmManualFormRef.value) {
        llmManualFormRef.value.clearValidate();
      }
    } else {
      // 关闭人工评估时，清空LLM评估表单验证
      if (llmFormRef.value) {
        llmFormRef.value.clearValidate();
      }
    }
  });
});

// 处理LLM对话框关闭
function handleLlmDialogClose() {
  // 清除表单验证状态
  if (llmFormRef.value) {
    llmFormRef.value.clearValidate();
  }
  if (llmManualFormRef.value) {
    llmManualFormRef.value.clearValidate();
  }

  // 清除LLM表单数据
  counter.llmFormData = {
    type_id: null,
    model_id: null,
    suite_sequence_id: null,
    project_id: null,
    suite_id: null,
  };

  // 清除人工评估表单数据
  counter.manualForm = {
    project_id: null,
    suite_id: null,
    test_case_id: null,
    response: '',
    score: null,
    remark: ''
  };

  // 清除相关选项数据
  counter.formLabelAlign3Options = [];
  counter.SequenceData = [];
  counter.caseOptions = [];

  // 重置人工评估开关状态
  counter.value90 = false;
  counter.optionspg1 = true;
  counter.rengongval = false;

  // 调用原有的关闭处理
  counter.handleClose111();
}

</script>
<style scoped>
.rag-result-dialog {
  --el-dialog-bg-color: #fafbfc; /* 弹窗背景色 */
  .el-dialog__body {
    padding: 24px 32px; /* 增加内边距 */
  }

  .rag-content {
    background: #ffffff; /* 内容区域背景 */
    border-radius: 8px;
    padding: 16px;
  }

  .cell-item {
    display: flex;
    align-items: center;
  }

  .text {
    font-size: 14px;
  }

  .item {
    padding: 18px 0;
  }

  .box-card {
    width: 480px;
  }
  .cards-section {
    display: flex;
    justify-content: space-around;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .card {
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    flex: 1;
    min-width: 200px;
    text-align: center;
    transition: transform 0.3s ease;
  }

  .card:hover {
    transform: translateY(-5px);
  }

  .card h3 {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 0.5rem;
  }

  .card .count {
    font-size: 1.8rem;
    color: #333;
    font-weight: bold;
  }
}
</style>
