<template>
    <el-row >
      <el-col :span="24" style="height:100%">
        <el-row style="margin-left: 0%; margin-top: 1%">

          <el-select
          style="width: 10%"
        v-model="projectValue"
        filterable
        clearable
        remote
        reserve-keyword
        placeholder="项目名称"
        :remote-method="remoteMethod"
        :loading="loading"
        @focus="requestServiceList()"
      >
        <el-option
          v-for="item in options"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

          <el-button style="color: #615ced; margin-left: 1%" plain @click="projectquery()">查询</el-button>
          <!-- 为重置按钮添加@click事件 -->
          <el-button style="color: #615ced" plain @click="resetQuery">重置</el-button>
          <el-row style="margin-left: 1%">
            <el-button style="color: #615ced" @click="testAdd" plain
              >新增</el-button
            >

          </el-row>
         </el-row>

        <el-table
          :data="Case"
          style="width: 100%; margin-top: 1%; margin-left: 0%; height: 100%"
        >

          <el-table-column
            prop="id"
            label="项目ID"
            min-width="5%"
            sortable
            show-overflow-tooltip
          >
          </el-table-column>

          <el-table-column
            prop="name"
            label="项目名称"
            min-width="10%"
            sortable
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="creator__username"
            label="创建人"
            min-width="10%"
            show-overflow-tooltip
          >
          </el-table-column>

          <el-table-column
            prop="description"
            label="描述"
            min-width="10%"
            sortable
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            prop="create_time"
            label="创建时间"
            min-width="10%"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="update_time"
            label="更新时间"
            min-width="10%"
            sortable
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    type="info"
                    size="small"
                    link
                    @click="handleDetail(row)"
                  >
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handlerun(row)"
                  >
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="del(row)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
        </el-table>
        <el-footer style="float: right;background: #fff;width: 100%;" >
          <el-pagination
    :page-size=20
    :pager-count="11"
    layout="prev, pager, next"
    :total=serviceListTotal
    @current-change="handleCurrentChange"
   style="float: right;margin-top: 0.8%;"
  />
        </el-footer>



      </el-col>


      <el-dialog
        title="新增项目"
        :model-value="dialogVisible"
        :before-close="handleClose"
        width="40%"
        height="90%"
      >

      <el-form
      ref="ruleFormRef"
      :model="projectRuleForm"
      status-icon
      :rules="rulesadd"
      label-width="120px"
      class="demo-ruleForm"
    >

    <el-form-item label="项目名称" prop="name">
      <el-input v-model="projectRuleForm.name"  autocomplete="off" style="width: 30%;" />
    </el-form-item>
    <el-form-item label="描述" >
      <el-input
        v-model="projectRuleForm.description"

        autocomplete="off"
        style="width: 30%;"
      />
    </el-form-item>

  </el-form>


    <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog()">取消</el-button>
          <el-button @click="submitForm(ruleFormRef)">
            保存
          </el-button>
        </span>
      </template>
      </el-dialog>
      <!-- 编辑页面 -->
      <el-dialog
        title="编辑"
        :model-value="updateVisible"
        :before-close="updateClose"
        width="50%"
        height="90%"
      >

    <el-form
      ref="ruleFormRef"
    :model="updateForm"
    status-icon
    :rules="rulesadd"
    label-width="120px"
    class="demo-ruleForm"
  >

    <el-form-item label="项目名称" prop="name">
      <el-input v-model="updateForm.name"  autocomplete="off" style="width: 30%;" />
    </el-form-item>
    <el-form-item label="描述" >
      <el-input
        v-model="updateForm.description"

        autocomplete="off"
        style="width: 30%;"
      />
    </el-form-item>

  </el-form>





    <template #footer>
        <span class="dialog-footer">
          <el-button @click="updateClose">取消</el-button>
          <el-button @click="saveBut(ruleFormRef)">
            保存
          </el-button>
        </span>
      </template>
      </el-dialog>

      <!-- 详情对话框 -->
      <el-dialog
        title="项目详情"
        :model-value="detailVisible"
        :before-close="handleDetailClose"
        width="60%"
        height="90%"
      >
        <div class="project-detail">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目ID">
              {{ detailData.id }}
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
              {{ detailData.name }}
            </el-descriptions-item>
            <el-descriptions-item label="创建人">
              {{ detailData.creator__username }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ detailData.create_time }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ detailData.update_time }}
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              {{ detailData.description || '暂无描述' }}
            </el-descriptions-item>
          </el-descriptions>

        </div>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleDetailClose">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </el-row>




  </template>

  <script setup>
  import dayjs from 'dayjs';
  import { onMounted, ref ,reactive} from "vue";
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { View, Edit, Delete } from '@element-plus/icons-vue'
  import { serviceList,addProject, updateProject ,delProject,getProjectList } from '@/api/projectApi';

const options = ref([])
const value = ref([])
const loading = ref(false)
let states = ref([])
const rulesadd = reactive({
  name: [
    { required: true, message: '项目名称为必填项', trigger: 'blur' },
    { min: 2, max: 20, message: '项目名称长度需在2-20字符之间', trigger: 'blur' }
  ]
})
let projectValue=ref('')
  let dialogVisible = ref(false);
  let updateVisible= ref(false);
  let detailVisible = ref(false);
  let serviceListPage=ref(1);
  let serviceListTotal=ref(0);

  let updateForm = ref({

    name: "",
    description: "",
    id: null
  });

  // 详情数据
  let detailData = ref({});
  let projectStats = ref({});

  function closeDialog() {
  dialogVisible.value = false
  projectRuleForm.value = {
    name: '',
    description: ''
  }
  // 清除表单验证状态
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate()
  }
}
  function resetQuery() {
  // 清空查询条件（项目名称选择值）
  projectValue.value = '';
  // 刷新列表数据（调用现有请求方法）
  requestServiceList();
}
function projectquery() {
  console.log("projectValue==",projectValue.value);
  if(projectValue.value===""){
   ElMessage.error('请选择项目');
  }else{

   let id=projectValue.value

   getProjectList(id).then((res) => {
serviceListTotal.value=res.total
Case.value=res.resp.map(item => {
    return {
      ...item,
      create_time:dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss'),
      update_time:dayjs(item.update_time).format('YYYY-MM-DD HH:mm:ss')
    }
  })

});
  }



}
// 二次确认删除功能
async function del(val) {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目 "${val.name}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    );

    // 执行删除操作
    const res = await delProject({id: val.id});
    if (res.success) {
      ElMessage.success('删除成功');
      requestServiceList();
    } else {
      ElMessage.error(res.msg || '删除失败');
    }
  } catch (error) {
    // 用户取消删除或发生错误
    if (error !== 'cancel') {
      ElMessage.error('删除操作失败');
    }
  }
}
  let Case = ref([]);
  function handlerun(val){
 updateVisible.value=true;
 updateForm.value = Object.assign({}, val);
  }

  // 处理详情按钮点击
  async function handleDetail(row) {
    try {
      detailVisible.value = true;
      detailData.value = Object.assign({}, row);

      // 从 getProjectList 接口获取详细数据
      const res = await getProjectList(row.id);
      if (res && res.resp && res.resp.length > 0) {
        const projectDetail = res.resp[0];

        // 更新详情数据，格式化时间
        detailData.value = {
          ...projectDetail,
          create_time: dayjs(projectDetail.create_time).format('YYYY-MM-DD HH:mm:ss'),
          update_time: dayjs(projectDetail.update_time).format('YYYY-MM-DD HH:mm:ss')
        };

        // 设置项目统计数据（如果接口返回了这些字段）
        projectStats.value = {
          requirement_count: projectDetail.requirement_count || 0,
          suite_count: projectDetail.suite_count || 0,
          test_case_count: projectDetail.test_case_count || 0
        };
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      ElMessage.error('获取项目详情失败');
    }
  }

  // 关闭详情对话框
  function handleDetailClose() {
    detailVisible.value = false;
    detailData.value = {};
    projectStats.value = {};
  }
  function saveBut(element) {
    let data = {
    id:updateForm.value.id,
    name: updateForm.value.name,
    description:updateForm.value.description
   }


  element.validate((valid) => {
    if (valid) {
      updateProject(data).then((res) => {

if(res.success){
  updateVisible.value = false;
  requestServiceList()
}else{
  ElMessage.error(res.message)
}

})
    }


})






  }

  function testAdd() {
    dialogVisible.value = true;
    projectRuleForm.value = {
      name: '',
      description: ''
    }
    // 清除表单验证状态
    setTimeout(() => {
      if (ruleFormRef.value) {
        ruleFormRef.value.clearValidate()
      }
    }, 0)
  }

  function handleClose() {
    dialogVisible.value = false;
    projectRuleForm.value = {
      name: '',
      description: ''
    }
    // 清除表单验证状态
    if (ruleFormRef.value) {
      ruleFormRef.value.clearValidate()
    }
  }
  function updateClose() {
    updateVisible.value = false;
      if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate()
  }

  }
  function handleCurrentChange(val) {
    serviceListPage.value=val;
    requestServiceList();
  }

  async  function requestServiceList(val) {
   //请求项目列表接口
   let params = {
      page: serviceListPage.value,
      page_size: val?val:20


   }
   await serviceList(params).then((res) => {

Case.value=res.resp.map(item => {
    return {
      ...item,
      create_time:dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss'),
      update_time:dayjs(item.update_time).format('YYYY-MM-DD HH:mm:ss')
    }
  }),
serviceListTotal.value=res.total
states.value=res.resp.map(item => {
    return {
      label:item.name,
      value:item.id

    }
  })

});
  }

  // addProject, updateProject ,delProject
  let projectRuleForm= ref({
    name:'',
    description:''
  })

const ruleFormRef = ref()

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      addProject(projectRuleForm.value).then((res) => {

if(res.success){
  dialogVisible.value = false;
  requestServiceList()
}else{
  ElMessage.error(res.msg)

}

})
    } else {
      console.log('error submit!', fields)
    }
  })
}
const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      options.value = states.value.filter((item) => {
        if(item.label.toLowerCase().includes(query.toLowerCase())){
          return item.value
        }

      })
    }, 200)


  } else {
    options.value = []
  }
}

  onMounted(() => {
    requestServiceList()

})

  </script>

  <style scoped>
  .projectinput:before {
    content: "项目名称";
  }

  /* 操作按钮样式 */
  .action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  /* 详情页面样式 */
  .project-detail {
    padding: 20px 0;
  }

  .project-stats h3 {
    margin-bottom: 16px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
  }

  .stat-card {
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .stat-item {
    padding: 20px;
  }

  .stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  /* 描述信息样式 */
  :deep(.el-descriptions__body) {
    background-color: #fafafa;
  }

  :deep(.el-descriptions__label) {
    font-weight: 600;
    color: #333;
  }

  :deep(.el-descriptions__content) {
    color: #666;
  }
  </style>


