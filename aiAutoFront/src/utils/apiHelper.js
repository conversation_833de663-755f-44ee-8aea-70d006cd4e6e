/**
 * API请求辅助工具
 */
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

/**
 * 通用API请求处理器
 * @param {Function} apiFunction - API函数
 * @param {Object} params - 请求参数
 * @param {Object} options - 配置选项
 * @returns {Promise}
 */
export const apiRequest = async (apiFunction, params = {}, options = {}) => {
  const {
    showLoading = false,
    showSuccess = false,
    showError = true,
    successMessage = '操作成功',
    errorMessage = '操作失败',
    loadingText = '加载中...'
  } = options

  try {
    if (showLoading) {
      // 可以在这里添加loading状态
    }

    const response = await apiFunction(params)
    
    if (response.success) {
      if (showSuccess) {
        ElMessage.success(successMessage)
      }
      return response
    } else {
      if (showError) {
        ElMessage.error(response.msg || errorMessage)
      }
      throw new Error(response.msg || errorMessage)
    }
  } catch (error) {
    if (showError) {
      ElMessage.error(error.message || errorMessage)
    }
    throw error
  }
}

/**
 * 分页数据处理器
 * @param {Object} response - API响应
 * @param {Function} formatter - 数据格式化函数
 * @returns {Object}
 */
export const handlePaginationResponse = (response, formatter = null) => {
  if (!response.success) {
    return {
      data: [],
      total: 0,
      currentPage: 1
    }
  }

  let data = response.resp || []
  
  // 如果有格式化函数，应用格式化
  if (formatter && typeof formatter === 'function') {
    data = data.map(formatter)
  }

  return {
    data,
    total: response.total || 0,
    currentPage: response.page || 1,
    pageSize: response.size || 20
  }
}

/**
 * 时间格式化器
 * @param {string} timeStr - 时间字符串
 * @param {string} format - 格式化模板
 * @returns {string}
 */
export const formatTime = (timeStr, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!timeStr) return ''
  return dayjs(timeStr).format(format)
}

/**
 * 通用数据格式化器
 * @param {Object} item - 数据项
 * @param {Array} timeFields - 需要格式化的时间字段
 * @returns {Object}
 */
export const formatDataItem = (item, timeFields = ['create_time', 'update_time', 'created_time']) => {
  const formatted = { ...item }
  
  timeFields.forEach(field => {
    if (formatted[field]) {
      formatted[field] = formatTime(formatted[field])
    }
  })
  
  return formatted
}

/**
 * 确认对话框
 * @param {string} message - 确认消息
 * @param {string} title - 对话框标题
 * @param {Object} options - 配置选项
 * @returns {Promise}
 */
export const confirmDialog = (message, title = '确认操作', options = {}) => {
  const {
    confirmButtonText = '确定',
    cancelButtonText = '取消',
    type = 'warning'
  } = options

  return ElMessageBox.confirm(message, title, {
    confirmButtonText,
    cancelButtonText,
    type
  })
}

/**
 * 删除确认对话框
 * @param {string} itemName - 要删除的项目名称
 * @returns {Promise}
 */
export const deleteConfirm = (itemName = '该项') => {
  return confirmDialog(
    `确定要删除${itemName}吗？此操作不可撤销。`,
    '删除确认',
    {
      type: 'warning',
      confirmButtonText: '删除',
      cancelButtonText: '取消'
    }
  )
}

/**
 * 表单提交处理器
 * @param {Object} formRef - 表单引用
 * @param {Function} submitFunction - 提交函数
 * @param {Object} options - 配置选项
 * @returns {Promise}
 */
export const handleFormSubmit = async (formRef, submitFunction, options = {}) => {
  const {
    showSuccess = true,
    successMessage = '操作成功',
    validateFirst = true
  } = options

  try {
    // 表单验证
    if (validateFirst && formRef) {
      await formRef.validate()
    }

    // 执行提交
    const result = await submitFunction()
    
    if (showSuccess) {
      ElMessage.success(successMessage)
    }
    
    return result
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(error.message || '操作失败')
    }
    throw error
  }
}

/**
 * 列表操作处理器
 */
export class ListHandler {
  constructor(options = {}) {
    this.pagination = {
      currentPage: 1,
      pageSize: 20,
      total: 0,
      ...options.pagination
    }
    this.loading = false
    this.data = []
    this.apiFunction = options.apiFunction
    this.formatter = options.formatter
  }

  // 加载数据
  async loadData(params = {}) {
    if (!this.apiFunction) {
      throw new Error('API函数未设置')
    }

    this.loading = true
    try {
      const requestParams = {
        page: this.pagination.currentPage,
        size: this.pagination.pageSize,
        ...params
      }

      const response = await this.apiFunction(requestParams)
      const result = handlePaginationResponse(response, this.formatter)
      
      this.data = result.data
      this.pagination.total = result.total
      
      return result
    } finally {
      this.loading = false
    }
  }

  // 分页变化
  async handlePageChange(page) {
    this.pagination.currentPage = page
    return await this.loadData()
  }

  // 页面大小变化
  async handleSizeChange(size) {
    this.pagination.pageSize = size
    this.pagination.currentPage = 1
    return await this.loadData()
  }

  // 刷新数据
  async refresh() {
    return await this.loadData()
  }

  // 重置分页
  reset() {
    this.pagination.currentPage = 1
    this.data = []
    this.pagination.total = 0
  }
}

/**
 * 选项数据格式化器
 * @param {Array} data - 原始数据
 * @param {Object} mapping - 字段映射
 * @returns {Array}
 */
export const formatOptions = (data, mapping = { label: 'name', value: 'id' }) => {
  if (!Array.isArray(data)) return []
  
  return data.map(item => ({
    label: item[mapping.label] || item.label || item.name,
    value: item[mapping.value] || item.value || item.id,
    ...item
  }))
}

/**
 * 级联选择器数据格式化器
 * @param {Array} data - 原始数据
 * @param {Object} mapping - 字段映射
 * @returns {Array}
 */
export const formatCascaderOptions = (data, mapping = { label: 'name', value: 'id', children: 'children' }) => {
  if (!Array.isArray(data)) return []
  
  return data.map(item => ({
    label: item[mapping.label] || item.label || item.name,
    value: item[mapping.value] || item.value || item.id,
    children: item[mapping.children] ? formatCascaderOptions(item[mapping.children], mapping) : undefined,
    ...item
  }))
}
