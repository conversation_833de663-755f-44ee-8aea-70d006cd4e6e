/**
 * 工具函数入口文件
 */

// 验证工具
export * from './validation'

// API辅助工具
export * from './apiHelper'

// 日期工具
export * from './date'

// 性能工具
export * from './performance'

// 响应式工具
export * from './responsive'

// 常用工具函数
export const utils = {
  // 深拷贝
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
  },

  // 防抖
  debounce(func, wait, immediate = false) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        timeout = null
        if (!immediate) func.apply(this, args)
      }
      const callNow = immediate && !timeout
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      if (callNow) func.apply(this, args)
    }
  },

  // 节流
  throttle(func, limit) {
    let inThrottle
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // 生成UUID
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 下载文件
  downloadFile(url, filename) {
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  },

  // 复制到剪贴板
  async copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch (err) {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        return true
      } catch (err) {
        return false
      } finally {
        document.body.removeChild(textArea)
      }
    }
  },

  // 获取URL参数
  getUrlParams(url = window.location.href) {
    const params = {}
    const urlObj = new URL(url)
    for (const [key, value] of urlObj.searchParams) {
      params[key] = value
    }
    return params
  },

  // 设置URL参数
  setUrlParams(params, url = window.location.href) {
    const urlObj = new URL(url)
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined) {
        urlObj.searchParams.set(key, params[key])
      } else {
        urlObj.searchParams.delete(key)
      }
    })
    return urlObj.toString()
  },

  // 数组去重
  uniqueArray(arr, key = null) {
    if (!key) {
      return [...new Set(arr)]
    }
    const seen = new Set()
    return arr.filter(item => {
      const value = item[key]
      if (seen.has(value)) {
        return false
      }
      seen.add(value)
      return true
    })
  },

  // 数组分组
  groupBy(arr, key) {
    return arr.reduce((groups, item) => {
      const group = item[key]
      groups[group] = groups[group] || []
      groups[group].push(item)
      return groups
    }, {})
  },

  // 树形数据扁平化
  flattenTree(tree, childrenKey = 'children') {
    const result = []
    const stack = [...tree]
    
    while (stack.length) {
      const node = stack.pop()
      result.push(node)
      
      if (node[childrenKey] && node[childrenKey].length) {
        stack.push(...node[childrenKey])
      }
    }
    
    return result
  },

  // 扁平数据转树形
  arrayToTree(arr, idKey = 'id', parentKey = 'parentId', childrenKey = 'children') {
    const map = {}
    const roots = []
    
    // 创建映射
    arr.forEach(item => {
      map[item[idKey]] = { ...item, [childrenKey]: [] }
    })
    
    // 构建树形结构
    arr.forEach(item => {
      const node = map[item[idKey]]
      if (item[parentKey] && map[item[parentKey]]) {
        map[item[parentKey]][childrenKey].push(node)
      } else {
        roots.push(node)
      }
    })
    
    return roots
  }
}

export default utils
