/**
 * 表单验证工具函数
 */

// 常用正则表达式
export const REGEX = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^1[3-9]\d{9}$/,
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  url: /^https?:\/\/.+/,
  number: /^\d+$/,
  decimal: /^\d+(\.\d+)?$/,
  chinese: /^[\u4e00-\u9fa5]+$/,
  englishAndNumber: /^[a-zA-Z0-9]+$/
}

// 通用验证规则生成器
export const createRules = {
  // 必填验证
  required(message = '此项为必填项', trigger = 'blur') {
    return { required: true, message, trigger }
  },

  // 长度验证
  length(min, max, message, trigger = 'blur') {
    const rule = { trigger }
    if (min !== undefined && max !== undefined) {
      rule.min = min
      rule.max = max
      rule.message = message || `长度在 ${min} 到 ${max} 个字符`
    } else if (min !== undefined) {
      rule.min = min
      rule.message = message || `最少 ${min} 个字符`
    } else if (max !== undefined) {
      rule.max = max
      rule.message = message || `最多 ${max} 个字符`
    }
    return rule
  },

  // 邮箱验证
  email(message = '请输入正确的邮箱地址', trigger = 'blur') {
    return {
      pattern: REGEX.email,
      message,
      trigger
    }
  },

  // 手机号验证
  phone(message = '请输入正确的手机号', trigger = 'blur') {
    return {
      pattern: REGEX.phone,
      message,
      trigger
    }
  },

  // 数字验证
  number(message = '请输入数字', trigger = 'blur') {
    return {
      pattern: REGEX.number,
      message,
      trigger
    }
  },

  // 小数验证
  decimal(message = '请输入数字', trigger = 'blur') {
    return {
      pattern: REGEX.decimal,
      message,
      trigger
    }
  },

  // 自定义验证器
  custom(validator, message, trigger = 'blur') {
    return {
      validator: (rule, value, callback) => {
        if (validator(value)) {
          callback()
        } else {
          callback(new Error(message))
        }
      },
      trigger
    }
  }
}

// 分数验证器（0-1之间）
export const scoreValidator = (rule, value, callback) => {
  if (value === '' || value === null || value === undefined) {
    callback()
    return
  }

  const numValue = parseFloat(value)
  
  if (isNaN(numValue)) {
    callback(new Error('分数必须为数字'))
    return
  }

  if (numValue < 0) {
    callback(new Error('分数不能为负数'))
    return
  }

  if (numValue > 1) {
    callback(new Error('分数不能大于1'))
    return
  }

  callback()
}

// 常用表单验证规则集合
export const commonRules = {
  // 项目名称
  projectName: [
    createRules.required('请输入项目名称'),
    createRules.length(1, 50, '项目名称长度在1-50个字符之间')
  ],

  // 项目描述
  projectDescription: [
    createRules.length(0, 200, '项目描述最多200个字符')
  ],

  // 用例集名称
  suiteName: [
    createRules.required('请输入用例集名称'),
    createRules.length(1, 100, '用例集名称长度在1-100个字符之间')
  ],

  // 版本号
  version: [
    createRules.required('请输入版本号'),
    createRules.length(1, 20, '版本号长度在1-20个字符之间')
  ],

  // 分数（0-1之间）
  score: [
    {
      validator: scoreValidator,
      trigger: 'blur'
    }
  ],

  // 关键字
  keyword: [
    createRules.length(0, 50, '关键字最多50个字符')
  ],

  // 邮箱
  email: [
    createRules.required('请输入邮箱地址'),
    createRules.email()
  ],

  // 手机号
  phone: [
    createRules.required('请输入手机号'),
    createRules.phone()
  ],

  // 必选项目
  projectId: [
    createRules.required('请选择项目', 'change')
  ],

  // 必选用例集
  suiteId: [
    createRules.required('请选择用例集', 'change')
  ],

  // 必选模型
  modelId: [
    createRules.required('请选择模型', 'change')
  ],

  // 物料选择
  material: [
    {
      required: true,
      message: '请选择关联物料',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (value !== null && value !== undefined) {
          callback()
        } else {
          callback(new Error('请选择关联物料'))
        }
      }
    }
  ]
}

// 表单验证工具类
export class FormValidator {
  constructor(formRef) {
    this.formRef = formRef
  }

  // 验证表单
  async validate() {
    if (!this.formRef) {
      throw new Error('表单引用不存在')
    }
    return await this.formRef.validate()
  }

  // 验证指定字段
  async validateField(prop) {
    if (!this.formRef) {
      throw new Error('表单引用不存在')
    }
    return await this.formRef.validateField(prop)
  }

  // 重置表单
  resetFields() {
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  // 清除验证
  clearValidate(props) {
    if (this.formRef) {
      this.formRef.clearValidate(props)
    }
  }
}

// 分数格式化和验证工具
export const scoreUtils = {
  // 验证分数输入
  validateScore(value) {
    if (value === '' || value === null || value === undefined) {
      return { valid: true, value: value }
    }

    const numValue = parseFloat(value)

    if (isNaN(numValue)) {
      return { valid: false, message: '分数必须为数字', value: '' }
    }

    if (numValue < 0) {
      return { valid: false, message: '分数不能为负数', value: 0 }
    }

    if (numValue > 1) {
      return { valid: false, message: '分数不能大于1', value: 1 }
    }

    // 保留两位小数
    const formattedValue = Math.round(numValue * 100) / 100
    return { valid: true, value: formattedValue }
  },

  // 格式化分数显示
  formatScore(value) {
    if (value === null || value === undefined || value === '') {
      return ''
    }
    const numValue = parseFloat(value)
    return isNaN(numValue) ? '' : numValue.toFixed(2)
  }
}
