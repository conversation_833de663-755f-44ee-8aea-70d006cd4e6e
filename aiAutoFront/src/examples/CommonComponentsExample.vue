<template>
  <div class="common-components-example">
    <h2>公共组件使用示例</h2>
    
    <!-- 搜索表单示例 -->
    <SearchForm
      v-model="searchParams"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    >
      <el-form-item label="项目名称">
        <el-input
          v-model="searchParams.projectName"
          placeholder="请输入项目名称"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="状态">
        <el-select
          v-model="searchParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="进行中" value="active" />
          <el-option label="已完成" value="completed" />
          <el-option label="已暂停" value="paused" />
        </el-select>
      </el-form-item>
    </SearchForm>

    <!-- 表格示例 -->
    <BaseTable
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :actions="tableActions"
      @action="handleTableAction"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    >
      <!-- 自定义状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
      
      <!-- 自定义创建时间列 -->
      <template #created_time="{ row }">
        {{ formatTime(row.created_time) }}
      </template>
    </BaseTable>

    <!-- 对话框示例 -->
    <BaseDialog
      v-model="dialogVisible"
      title="编辑项目"
      width="600px"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <BaseForm
        ref="formRef"
        v-model="formData"
        :rules="formRules"
      >
        <el-form-item label="项目名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入项目名称"
          />
        </el-form-item>
        
        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入项目描述"
            :rows="4"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="请选择状态"
          >
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已暂停" value="paused" />
          </el-select>
        </el-form-item>
      </BaseForm>
    </BaseDialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  commonRules, 
  apiRequest, 
  formatTime, 
  deleteConfirm,
  ListHandler 
} from '@/utils'

// 搜索参数
const searchParams = ref({
  projectName: '',
  status: null
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表格列配置
const tableColumns = [
  {
    prop: 'name',
    label: '项目名称',
    minWidth: '150'
  },
  {
    prop: 'description',
    label: '项目描述',
    minWidth: '200',
    showOverflowTooltip: true
  },
  {
    prop: 'status',
    label: '状态',
    width: '100'
  },
  {
    prop: 'created_time',
    label: '创建时间',
    width: '180'
  }
]

// 表格操作按钮
const tableActions = [
  {
    key: 'edit',
    label: '编辑',
    type: 'primary',
    size: 'small'
  },
  {
    key: 'delete',
    label: '删除',
    type: 'danger',
    size: 'small'
  }
]

// 对话框
const dialogVisible = ref(false)
const formRef = ref(null)

// 表单数据
const formData = ref({
  id: null,
  name: '',
  description: '',
  status: 'active'
})

// 表单验证规则
const formRules = reactive({
  name: commonRules.projectName,
  description: commonRules.projectDescription,
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 模拟API函数
const mockApi = {
  getProjects: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = [
          {
            id: 1,
            name: '项目A',
            description: '这是项目A的描述',
            status: 'active',
            created_time: '2024-01-01 10:00:00'
          },
          {
            id: 2,
            name: '项目B',
            description: '这是项目B的描述',
            status: 'completed',
            created_time: '2024-01-02 11:00:00'
          }
        ]
        
        resolve({
          success: true,
          resp: mockData,
          total: mockData.length
        })
      }, 1000)
    })
  },
  
  updateProject: (data) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          msg: '更新成功'
        })
      }, 500)
    })
  },
  
  deleteProject: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          msg: '删除成功'
        })
      }, 500)
    })
  }
}

// 创建列表处理器
const listHandler = new ListHandler({
  apiFunction: mockApi.getProjects,
  pagination
})

// 搜索处理
const handleSearch = async (params) => {
  loading.value = true
  try {
    const result = await listHandler.loadData(params)
    tableData.value = result.data
  } finally {
    loading.value = false
  }
}

// 重置处理
const handleReset = async () => {
  await handleSearch({})
}

// 分页处理
const handlePageChange = async (page) => {
  await listHandler.handlePageChange(page)
  tableData.value = listHandler.data
}

const handleSizeChange = async (size) => {
  await listHandler.handleSizeChange(size)
  tableData.value = listHandler.data
}

// 表格操作处理
const handleTableAction = async (action, row) => {
  switch (action) {
    case 'edit':
      handleEdit(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

// 编辑处理
const handleEdit = (row) => {
  formData.value = { ...row }
  dialogVisible.value = true
}

// 删除处理
const handleDelete = async (row) => {
  try {
    await deleteConfirm(row.name)
    await apiRequest(mockApi.deleteProject, row.id, {
      showSuccess: true,
      successMessage: '删除成功'
    })
    await handleSearch(searchParams.value)
  } catch (error) {
    // 用户取消删除或删除失败
  }
}

// 对话框确认
const handleDialogConfirm = async () => {
  try {
    await apiRequest(mockApi.updateProject, formData.value, {
      showSuccess: true,
      successMessage: '更新成功'
    })
    dialogVisible.value = false
    await handleSearch(searchParams.value)
  } catch (error) {
    // 更新失败
  }
}

// 对话框取消
const handleDialogCancel = () => {
  formRef.value?.resetFields()
}

// 状态相关工具函数
const getStatusType = (status) => {
  const typeMap = {
    active: 'success',
    completed: 'info',
    paused: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    active: '进行中',
    completed: '已完成',
    paused: '已暂停'
  }
  return textMap[status] || '未知'
}

// 初始化
onMounted(() => {
  handleSearch({})
})
</script>

<style scoped>
.common-components-example {
  padding: 20px;
}

.common-components-example h2 {
  margin-bottom: 20px;
  color: #303133;
}
</style>
